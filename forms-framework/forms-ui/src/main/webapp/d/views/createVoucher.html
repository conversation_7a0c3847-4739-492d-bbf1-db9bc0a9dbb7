<style>
    textarea {
        border: 1px solid;
    }

    body, html {
        overflow-x: hidden;
    }
</style>
<div data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h2 class="text-center page-heading">Voucher</h2>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="scrollmenu">
                <a href="" data-ng-repeat="menu in menuList" data-ng-click="updateAction(menu.action)"
                   data-ng-class="{'menu-highlight' : menu.action == actionDetail.type}">
                    {{menu.name}}
                    <span class="badge" style="background-color: red"
                          data-ng-if="menu.action == 'acknowledge' && countMap.rejectedVoucher > 0">{{countMap.rejectedVoucher}}</span>
                </a>
            </div>
        </div>
    </div>
    <div class="walletId-container">
        <div data-ng-if="actionDetail.type == 'verify'">
            <div style="margin-top: 20px; text-align: center;">
                <h3>OTP Verification</h3>
            </div>
            <div data-ng-if="error" class="alert alert-danger" role="alert">{{errorMessage}}</div>
            <fieldset data-ng-disabled="otpDetails.otpSent">
                <div class="form-group">
                    <label for="userId">User Id</label>
                    <!--<select class="form-control" id="userId" name="userId" data-ng-model="authDetails.userId" required="required"
                            data-ng-options="employee.id as employee.name + ' - ' + employee.employeeCode for employee in unitEmployees"></select>-->
                    <input type="text" id="userId" data-ng-model="authDetails.userId" class="form-control" disabled/>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" name="password" id="password" class="form-control"
                           data-ng-model="authDetails.password" required="required"/>
                </div>
            </fieldset>
            <div class="form-actions" data-ng-if="!otpDetails.otpSent">
                <button type="button" data-ng-hide="loading" class="btn btn-primary"
                        data-ng-click="verifyUser()">Generate OTP
                </button>
                <div class="loader xs primary lfast" data-ng-show="loading"></div>
            </div>
            <div data-ng-if="otpDetails.otpSent">
                <div class="row">
                    <div class="col-xs-12">
                        <h4 style="text-decoration: underline; color: red;">
                            An OTP Has Been Sent On {{issuerDetails.name}}`s Mobile Number {{issuerDetails.contact}}.
                        </h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label for="OTP">Enter OTP </label>
                            <input type="text" name="OTP" id="OTP" class="form-control"
                                   data-ng-model="authDetails.otp" required="required" maxlength="4"/>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-bottom: 10px;">
                    <div class="col-xs-6">
                        <div class="form-actions">
                            <button type="button" class="btn btn-warning pull-right" data-ng-click="verifyOTP()">
                                Verify OTP
                            </button>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-actions">
                            <button type="button" data-ng-if="otpDetails.otpCount < 4" class="btn btn-success"
                                    data-ng-click="resendOtp()">
                                Resend OTP
                            </button>
                            <div class="loader xs primary lfast" data-ng-show="loading"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div data-ng-if="actionDetail.type == 'detail'"
             data-ng-include="'views/walletView.html'"></div>
        <!-- Issue Voucher  -->
        <div data-ng-if="actionDetail.type == 'create'">
            <form name="createVoucherForm" novalidate>
                <div class="row row-spacing" style="margin-top: 30px;">
                    <div class="col-xs-3">
                        <div class="form-group">
                            <label for="issuedTo">Select ACCOUNT*</label>
                            <select class="form-control"
                                    data-ng-model="selectedAccount.accountNo"
                                    data-ng-change="changeSelectAccount()"
                                    data-ng-options="account as account.accountNo for account in accounts"
                                    required="required"></select>
                        </div>
                    </div>
                    <div class="col-xs-3" data-ng-show ="selectedAccountType ==='EMPLOYEE'">
                        <div class="form-group">
                            <label for="issuedTo">Issued To</label>
                            <input class="form-control" type="text" data-ng-model="selfEmployee" disabled/>
                        </div>
                    </div>
                    <div class="col-xs-3" data-ng-show ="selectedAccountType ==='UNIT'">
                        <div class="form-group">
                            <label for="issuedTo">Issued To</label>
                            <!--<select class="form-control" id="issuedTo" name="issuedTo"
                                    data-ng-model="authDetails.issuedTo"
                                    data-ng-options="employee.id as employee.name + ' - ' + employee.employeeCode for employee in unitEmployees"
                                    required="required"></select>-->
                            <select data-ui-select2 class="form-control" id="issuedTo" name="issuedTo" data-ng-model="authDetails.issuedTo">
                                <option value=""></option>
                                <option data-ng-repeat="employee in unitEmployees" value="{{employee.id}}">
                                    {{employee.name + " - " + employee.employeeCode}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="col-xs-3">
                        <div class="form-group">
                            <label for="issuedReason">Issued Reason</label>
                            <select class="form-control" id="issuedReason" name="issuedReason"
                                    data-ng-model="voucherDetail.expenseMetadataId"
                                    data-ng-options="expense.id as expense.desc for expense in expenseType"
                                    required="required"></select>
                        </div>
                    </div>
                    <div class="col-xs-3">
                        <div class="form-group">
                            <label for="issueAmount">Issue Amount</label>
                            <input type="number" id="issueAmount" name="issueAmount"
                                   data-ng-model="voucherDetail.issuedAmount" class="form-control" required="required">
                        </div>
                    </div>
                </div>

                <div class="row row-spacing">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label for="issueComment">Comment(Optional)</label>
                            <textarea style="width: 100%;" rows="5" name="issueComment" id="issueComment"
                                      data-ng-model="voucherDetail.expenseDetail" class="form-control"> </textarea>
                        </div>
                    </div>
                </div>

                <div class="row alert alert-info"
                     data-ng-show="hasOneBccMapping !==undefined && hasOneBccMapping !==null && hasOneBccMapping">
                    <div class="col-xs-12">
                        <h4>The entire cost will be allocated to business cost center:{{businessCostCenter.bccCode}}_{{businessCostCenter.bccName}}</h4>
                    </div>
                </div>
                <!--<div class="row row-spacing" data-ng-show ="selectedAccount.businessCostCentersList !=undefined && selectedAccount.businessCostCentersList !=null && selectedAccount.businessCostCentersList.length>0 ">
                    <div class="col-xs-12">
                        <h4></h4>
                    </div>
                </div>-->
                <div class="col-xs-12">
                    <table class="table table-bordered table-striped" data-ng-show="voucherDetail.voucherCostCenterAllocations.length>0 ">
                        <tr>
                            <th>Business Cost Center Id</th>
                            <th>Business Cost Center Name</th>
                            <th>Allocated Amount</th>
                            <th>Action</th>
                        </tr>
                        <tr data-ng-repeat="voucherCostCenterMapping in filtered = (voucherDetail.voucherCostCenterAllocations | filter:search | orderBy : predicate :reverse) track by voucherCostCenterMapping.businessCostCenterId">
                            <td style="font-size: 12px">{{voucherCostCenterMapping.businessCostCenterId}}</td>
                            <td style="font-size: 12px">{{voucherCostCenterMapping.businessCostCenter}}</td>
                            <td style="font-size: 12px">{{voucherCostCenterMapping.allocatedIssuedAmount}}</td>
                            <td style="font-size: 12px">
                                <button class="btn btn-primary"
                                        data-ng-click="editVoucherCostCenterAllocation(voucherCostCenterMapping, voucherCostCenterMapping.businessCostCenterId)">
                                    Edit
                                </button>
                            </td>
                        </tr>

                    </table>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-12">
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary pull-right" data-ng-click="allocateCost()"
                                    data-ng-disabled="voucherDetail.issuedAmount=== undefined || voucherDetail.issuedAmount === null || voucherDetail.issuedAmount<=0 || isSuccessfulAllocation ===true"
                                    data-ng-hide="canAllocateCostToCafes===false">
                                Allocate Cost
                            </button>
                            <button type="button" class="btn btn-primary pull-right" style="margin-right: 10px;" data-ng-click="createVoucher()"
                                    data-ng-disabled="selectedAccountType == null || (canAllocateCostToCafes == true && isSuccessfulAllocation===false)">
                                Create Issue Note
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- Settle Voucher -->
        <div data-ng-if="actionDetail.type == 'settle'">
            <div class="row-spacing">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row" data-ng-if="false">
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <label for="voucherAccountNo" class="control-label">Select Unit Account </label>
                                    <select class="form-control" id="voucherAccountNo" name="voucherAccountNo"
                                            data-ng-model="searchVoucher.accountNo"
                                            data-ng-options="unit as unit.name for unit in units" required="required">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row-spacing">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label for="issuedTo">Select ACCOUNT*</label>
                                        <select class="form-control"
                                                data-ng-model="selectedAccount.accountNo"
                                                data-ng-options="account as account.accountNo for account in accounts"
                                                required="required"></select>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 form-group">
                            <label class="control-label" for="searchVoucherStatus">Voucher Status</label>
                            <select data-ng-model="searchVoucher.status"
                                    class='form-control' id="searchVoucherStatus"
                                    name="searchVoucherStatus">
                                <option value="PENDING_SETTLEMENT" selected>Pending Settlement</option>
                                <option value="CANCELLED">Cancelled</option>
                                <option value="AM_PENDING">AM Pending</option>
                                <option value="FINANCE_PENDING">Finance Pending</option>
                                <!-- <option value="PENDING_REJECTION">Rejected Pending</option> -->
                                <option value="REJECTED">Rejected</option>
                                <option value="APPROVED">Approved</option>
                            </select>
                        </div>
                        <div class="col-xs-4 form-group">
                            <label for="voucherStartDate" class="control-label">Start Date </label>
                            <p class="input-group">
                                <input type="text" class="form-control" id="voucherStartDate"
                                       uib-datepicker-popup="yyyy-MM-dd"
                                       data-ng-model="searchVoucher.startDate"
                                       is-open="searchVoucher.startDate.opened"
                                       datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                       data-ng-required="false" close-text="Close"/> <span
                                    class="input-group-btn">
								<button type="button" class="btn btn-default"
                                        data-ng-click="searchVoucher.startDate.opened=true">
									<i class="glyphicon glyphicon-calendar"></i>
								</button>
							</span>
                            </p>
                        </div>
                        <div class="col-xs-4 form-group">
                            <label for="voucherEndDate" class="control-label">End date
                            </label>
                            <p class="input-group">
                                <input type="text" class="form-control" id="voucherEndDate"
                                       uib-datepicker-popup="yyyy-MM-dd"
                                       ng-model="searchVoucher.endDate"
                                       is-open="searchVoucher.endDate.opened"
                                       datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                       ng-required="false" close-text="Close"/> <span
                                    class="input-group-btn">
								<button type="button" class="btn btn-default"
                                        data-ng-click="searchVoucher.endDate.opened=true">
									<i class="glyphicon glyphicon-calendar"></i>
								</button>
							</span>
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 form-group"
                             data-ng-if="searchVoucher.actionType != 'view'">
                            <button class="btn btn-primary pull-right"
                                    data-ng-click="getVoucherDetails(true, true)">Search Voucher
                            </button>
                            <button class="btn btn-danger"
                                    data-ng-click="resetSearchDetails()">Reset
                            </button>
                        </div>
                        <div class="col-xs-4 col-xs-offset-8"
                             data-ng-if="searchVoucher.actionType == 'view'">
                            <button class="btn  btn-info "
                                    data-ng-click="searchAction(null, 'back')">Back
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row-spacing"
                     data-ng-if="searchVoucher.actionType != 'view'">
                    <div class="col-xs-12">
                        <div data-ng-repeat="voucher in voucherList"
                             style="border: 5px solid; margin-bottom: 5px;">
                            <div class="panel panel-default" id="voucher_{{voucher.id}}">
                                <!-- View of Voucher Card -->
                                <div data-ng-include="'views/voucherCard.html'"></div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div data-ng-if="selectedVoucher == null || selectedVoucher.id != voucher.id">
                                            <div data-ng-if="voucher.currentStatus == 'PENDING_SETTLEMENT'">
                                                <div class="col-xs-4">
                                                    <button class="btn btn-success "
                                                            data-ng-click="searchAction(voucher,'settle')">Settle
                                                    </button>
                                                </div>
                                                <!--<div class="col-xs-4">
                                                    <button class="btn  btn-warning"
                                                            data-ng-click="searchAction(voucher,'cancel')"
                                                            data-ng-if="false">Cancel
                                                    </button>
                                                </div>-->
                                            </div>
                                            <div class="col-xs-4">
                                                <button class="btn  btn-info "
                                                        data-ng-click="searchAction(voucher, 'view')">View
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-xs-4 col-xs-offset-8"
                                             data-ng-if="selectedVoucher != null && selectedVoucher.id == voucher.id">
                                            <button class="btn  btn-info "
                                                    data-ng-click="searchAction(null, 'back')">Back
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--<div class="row"
                                 data-ng-if="searchVoucher.actionType == 'cancel' && selectedVoucher.id == voucher.id">
                                <div class="col-xs-12">
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label for="comment" class="control-label">Comment </label>
                                            <textarea style="width: 100%;" rows="5" name="comment"
                                                      id="comment" data-ng-model="voucherDetail.actionComment"
                                                      required="required"> </textarea>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-4 " style="margin-bottom: 5px;">
                                            <button class="btn  btn-warning"
                                                    data-ng-click="updateVoucher(true)">Cancel Voucher
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>-->
                            <div class="row"
                                 data-ng-if="searchVoucher.actionType =='settle' && selectedVoucher.id == voucher.id">
                                <div class="col-xs-12"
                                     style="margin-left: 10px; padding-right: 35px;">
                                    <div class="row row-spacing">
                                        <div class="col-xs-12">
                                            <div class="form-group">
                                                <label for="expenseAmount">Expense Amount</label><input
                                                    type="number" id="expenseAmount" name="expenseAmount"
                                                    data-ng-model="voucherDetail.expenseAmount"
                                                    class="form-control" required="required">
                                            </div>
                                        </div>
                                        <div class="col-xs-12" data-ng-if="voucherDetail.expenseType === 'COGS - Dairy'">
                                            <div class="form-group">
                                                <label for="grNumber">GR Number</label>
                                                <input
                                                    type="number" id="grNumber" name="grNumber"
                                                    data-ng-model="voucherDetail.grNumber"
                                                    class="form-control" required="required">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label for="comment" class="control-label">Comment </label>
                                            <textarea style="width: 100%;" rows="5" name="comment"
                                                      id="comment"
                                                      data-ng-model="voucherDetail.actionComment"> </textarea>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label for="voucherDate" class="control-label">Invoice Date</label>
                                            <input type="date" id="voucherDate" name="voucherDate"
                                                   data-ng-model="voucherDetail.voucherDate"
                                                   class="form-control" required="required">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-6"
                                             data-ng-if="!voucher.hasInvoice && voucher.invoiceMandatory">
                                            <form>
                                                <div class="row row-spacing">
                                                    <div class="col-xs-12 form-group">
                                                        <label class="control-label"> Invoice upload : </label>
                                                        <input type="file" file-model="voucherInvoice"
                                                               accept="image/*"/>
                                                    </div>
                                                </div>
                                            </form>
                                            <div class="form-group">
                                                <button class="btn btn-warning"
                                                        data-ng-click="uploadVoucherDoc('INVOICE', voucher, voucherInvoice)">
                                                    Upload Invoice
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-xs-6"
                                             data-ng-if="!voucher.hasSupporting && voucher.supportingMandatory">
                                            <form>
                                                <div class="row row-spacing">
                                                    <div class="col-xs-12 form-group">
                                                        <label class="control-label"> Supporting upload : </label>
                                                        <input type="file" file-model="voucherSupporting"
                                                               accept="image/*"/>
                                                    </div>
                                                </div>
                                            </form>
                                            <div class="form-group">
                                                <button class="btn btn-warning"
                                                        data-ng-click="uploadVoucherDoc('SUPPORTING', voucher, voucherSupporting)">
                                                    Upload Supporting
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-bottom: 10px;">
                                        <div class="col-xs-4">
                                            <button class="btn  btn-success" data-ng-click="updateVoucher(false)">Settle
                                                Voucher
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- View of Voucher  -->
                <div data-ng-if="searchVoucher.actionType == 'view'"
                     data-ng-include="'views/voucherView.html'"></div>
            </div>

            <!-- Acknowledge Voucher -->


            <!--<div class="row" data-ng-if="actionDetail.type == 'topup'">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-10 form-group">
                            <h3 class="text-center page-heading">Approved Amount :
                                {{walletData.topupAmount}}</h3>
                        </div>
                        <div class="col-xs-2 form-group">
                            <i class="fa fa-plus-circle" aria-hidden="true"
                                data-ng-click="denominationModal('topup', null, walletData.topupAmount)"
                                data-ng-if="walletData.topupAmount > 0"
                                style="color: black; font-size: 40px;"></i>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 col-xs-offset-8"
                            data-ng-if="searchVoucher.actionType == 'view'">
                            <button class="btn  btn-info "
                                data-ng-click="searchAction(null, 'back')">Back</button>
                        </div>
                    </div>
                    <div class="row" data-ng-if="voucherList.length == 0">
                        <div class="col-xs-12 action-comment">No Approved Vouchers</div>
                    </div>
                    <div data-ng-repeat="voucher in voucherList"
                        style="border: 5px solid; margin-bottom: 5px;"
                        data-ng-if="searchVoucher.actionType != 'view'">
                        <div class="panel panel-default">
                            &lt;!&ndash; View of Voucher Card &ndash;&gt;
                            <div data-ng-include="'views/voucherCard.html'"></div>
                            <div class="panel-body">
                                <div class="row">
                                    <div>
                                        <div class="col-xs-4">
                                            <button class="btn  btn-info pull-left"
                                                data-ng-click="searchAction(voucher, 'view')">View</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div data-ng-if="searchVoucher.actionType == 'view'"
                        data-ng-include="'views/voucherView.html'"></div>
                </div>
            </div>-->
        </div>
        <div data-ng-if="actionDetail.type == 'acknowledge'">
            <div class="row">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select ACCOUNT*</label>
                                <select class="form-control"
                                        data-ng-model="selectedAccount.accountNo"
                                        data-ng-options="account as account.accountNo for account in accounts"
                                        required="required"></select>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-12">
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary pull-right" style="margin-right: 10px;" data-ng-click="getVoucherDetails(true,false)"
                                    data-ng-disabled="selectedAccountType == null || (canAllocateCostToCafes == true && isSuccessfulAllocation===false)">
                                Get Voucher
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-12 form-group">
                        <h3 class="text-center page-heading">Rejected Pending
                            Vouchers</h3>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-4 col-xs-offset-8"
                         data-ng-if="searchVoucher.actionType == 'view'">
                        <button class="btn  btn-info "
                                data-ng-click="searchAction(null, 'back')">Back
                        </button>
                    </div>
                </div>
                <div class="row" data-ng-if="voucherList.length == 0">
                    <div class="col-xs-12 action-comment">No Pending Vouchers</div>
                </div>
                <div data-ng-repeat="voucher in voucherList"
                     style="border: 5px solid; margin-bottom: 5px;"
                     data-ng-if="searchVoucher.actionType != 'view'">
                    <div class="panel panel-default">
                        <!-- View of Voucher Card -->
                        <div data-ng-include="'views/voucherCard.html'"></div>
                        <div class="panel-body">
                            <div class="row">
                                <div>
                                    <div data-ng-if="voucher.currentStatus == 'PENDING_REJECTION' || voucher.currentStatus == 'PENDING_REJECTION_AM'
                                    || voucher.currentStatus == 'PENDING_REJECTION_FINANCE'">
                                        <div class="col-xs-6">
                                            <button class="btn btn-success"
                                                    data-ng-click="searchAction(voucher,'acknowledge')">Acknowledge
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-xs-6">
                                        <button class="btn  btn-info pull-right"
                                                data-ng-click="searchAction(voucher, 'view')">View
                                        </button>
                                    </div>
                                </div>
                                <div class="col-xs-4 col-xs-offset-8"
                                     data-ng-if="searchVoucher.actionType == 'view'">
                                    <button class="btn  btn-info pull-right"
                                            data-ng-click="searchAction(null, 'back')">Back
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!--<div class="row"
                             data-ng-if="false && searchVoucher.actionType == 'acknowledge' && selectedVoucher.id == voucher.id">
                            <div class="col-xs-12">
                                <div class="row">
                                    <div class="col-xs-12 form-group">
                                        <label for="comment" class="control-label">Comment </label>
                                        <textarea style="width: 100%;" rows="5" name="comment"
                                                  id="comment" data-ng-model="voucherDetail.actionComment"
                                                  required="required"> </textarea>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-4 " style="margin-bottom: 5px;">
                                        <button class="btn  btn-warning"
                                                data-ng-click="acknwoledgeVoucherRejection()">Acknowledge
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>-->
                    </div>
                </div>
                <div data-ng-if="searchVoucher.actionType == 'view'"
                     data-ng-include="'views/voucherView.html'"></div>
            </div>
        </div>
    </div>
</div>
    <!--modal to for voucher cost center allocation-->

    <script type="text/ng-template" id="openAllocateCostToBCCModalView.html">
        <div class="modal-content">
            <div class="modal-header" data-ng-init="init()">
                <h3 class="modal-title"><span style="text-transform: capitalize;">{{action}}</span> of Voucher
                    CostCenter Allocation Mappings</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group col-xs-6">
                        <label>Select Business Cost Center *</label>
                        <select class="form-control" ng-model="selectedBusinessCostCenter">
                            <option ng-repeat="bcc in storeBccList" value="{{bcc}}">
                                {{bcc}}
                            </option>
                        </select>
                    </div>
                    <div class="form-group col-xs-6">
                        <label> Enter Allocated Amount *</label>
                        <textarea class="form-control" rows="1" ng-model="amountAllocated" required></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" ng-click="cancel()">Close</button>
                <button class="btn btn-success" type="button"
                        data-ng-disabled="selectedBusinessCostCenter==null || amountAllocated==null"
                        ng-click="saveVoucherCostCenterAllocation(selectedBusinessCostCenter)">SUBMIT
                </button>
            </div>
        </div>
    </script>


    <script type="text/ng-template" id="editAllocateCostToBCCModalView.html">
        <div class="modal-content">
            <div class="modal-header" data-ng-init="init()">
                <h3 class="modal-title"><span style="text-transform: capitalize;">{{action}}</span> of Voucher
                    CostCenter Allocation Mappings</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <h5 style="position:relative;left:20px">Select Business Cost Centers</h5>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group col-xs-6">
                        <label>Select Business Cost Center *</label>
                        <select class="form-control" ng-model="selectedBusinessCostCenterName" ng-change="disableDeleteBtn()">
                            <option ng-repeat="bcc in storeBccList" value="{{bcc}}">
                                {{bcc}}
                            </option>
                        </select>
                    </div>
                    <div class="form-group col-xs-6">
                        <label> Enter Allocated Amount *</label>
                        <textarea class="form-control" rows="1"
                                  ng-model="selectedVoucherCostCenterAllocationAmt" ng-change="disableDeleteBtn()"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" type="button" ng-click="cancel()">Close</button>
                <button class="btn btn-danger" type="button"
                        data-ng-disabled="isDeleteBtnDisabled===true"
                        ng-click="removeVoucherCostCenterAllocation(selectedVoucherCostCenterAllocation)">DELETE
                </button>
                <button class="btn btn-success" type="button"
                        ng-click="updateVoucherCostCenterAllocation(selectedBusinessCostCenterName,selectedVoucherCostCenterAllocation)">SUBMIT
                </button>
            </div>
        </div>
    </script>