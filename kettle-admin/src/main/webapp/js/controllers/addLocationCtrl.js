/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("addLocationCtrl", function ($scope, $window, $http, $location, AppUtil) {

    $scope.init=function (){
        $scope.states=[]
        $scope.countryCodes=[
            {
                "id":1,
                "name":"IND"
            }
        ]
        $scope.statusList=["ACTIVE", "IN_ACTIVE"]
        $scope.functionalList=["YES","NO"]
        $scope.cityName = null;
        $scope.cityCode = null;
        $scope.selectedState = null;
        $scope.countryCode = null;
        $scope.locationStatus = null;
        $scope.isFunctional = null;
        $scope.getStates();
    }

    $scope.getStates=function (){
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmProductManagement.getState
        }).then(function success(response) {
            if(response.status === 200 && response.data){
                $scope.states=response.data;
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.saveLocation=function (){
        if($scope.cityName == null || $scope.cityName.trim().length ==0){
            bootbox.alert("Invalid City name")
            return;
        }
        if($scope.cityCode == null || $scope.cityCode.trim().length ==0){
            bootbox.alert("Invalid City Code")
            return;
        }
        if($scope.countryCode == null || $scope.cityCode.trim().length ==0){
            bootbox.alert("Invalid City Code")
            return;
        }
        if($scope.selectedState == null || $scope.selectedState.trim().length ==0){
            bootbox.alert("Invalid State")
            return;
        }
        if($scope.locationStatus == null || $scope.locationStatus.trim().length ==0){
            bootbox.alert("Invalid Status")
            return;
        }
        if($scope.isFunctional == null || $scope.isFunctional.trim().length ==0){
            bootbox.alert("Invalid Status")
            return;
        }
        if($scope.isFunctional === "YES"){
            $scope.isFunctional = true;
        }else{
            $scope.isFunctional = false;
        }
        var data ={
            "city":$scope.cityName,
            "cityCode":$scope.cityCode,
            "stateId":JSON.parse($scope.selectedState).id,
            "status":$scope.locationStatus,
            "functionalFlag":$scope.isFunctional,
            "countryId":JSON.parse($scope.countryCode).id
        }
        console.log("saving data ::::",data);
        $http({
            method: 'POST',
            url: AppUtil.restUrls.location.addLocation,
            data: data
        }).then(function success(response) {
            if(response.status === 200 && response.data){
                alert("Location added successfully");
                window.location.reload();
            }
        }, function error(response) {
            console.log("error:" + response);
            alert("Unable to Add location");
        });
    }

})
