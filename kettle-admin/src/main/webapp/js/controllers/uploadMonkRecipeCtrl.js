/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("uploadMonkRecipeCtrl", function ($rootScope, $scope, $http, AppUtil, $cookieStore, fileService, $timeout) {


    $scope.init = function () {
        //$scope.getRegions();
        $scope.getProfiles();
           $scope.userDetails=AppUtil.getUserValues();
        $scope.showUploaded = false;
        $scope.afterUploading=false;
        $scope.selectedProfileBasicDetail=null

    }

    $scope.getRegions = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.regions = [];
                response.data.map(function (region, index) {
                    $scope.regions.push({id: index, name: region, flag: false, list: []});
                });
            } else {
                bootbox.alert("Error getting unit regions.");
            }
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            console.log("error:" + response);
            $rootScope.showFullScreenLoader = false;
        });
    };

    $scope.getProfiles = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.getListDataByGrpAndCat+"?gName=MONK_RECIPE_DATA&cat=RECIPE_PROFILE"
            }).then(function success(response) {
                if (response.status === 200 && response.data != null) {
                    $scope.profiles = [];
                    response.data.map(function (profile, index) {
                        $scope.profiles.push(profile);
                    });
                } else {
                    bootbox.alert("Error getting recipe profiles.");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };


    $scope.setSelectedRegion = function (selectedRegion) {
        $scope.selectedRegion = selectedRegion;
    }

    $scope.setSelectedProfile = function (selectedProfile) {
                $scope.selectedProfile = selectedProfile;
        }
    $scope.fetchDetail= function(){
        $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.monkRecipeManagement.getMonkRecipeProfileBasicDetail+"?profile="+$scope.selectedProfile.code,
                        data:{name:"mmnvm"}
                    }).then(function success(response) {
                        if (response.status === 200 && response.data != null) {
                            $scope.selectedProfileBasicDetail = response.data;
                            $scope.selectedProfileBasicDetail.genetrationTime=moment($scope.selectedProfileBasicDetail.genetrationTime).format('YYYY-MM-DD')
                            $scope.selectedProfileBasicDetail.activationTime=moment($scope.selectedProfileBasicDetail.activationTime).format('YYYY-MM-DD')
                        } else {
                            bootbox.alert("Error getting recipe profiles basic detail.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
    }

    $scope.downloadSheet= function(){
        $rootScope.showFullScreenLoader = true;
                    $http({
                        method: 'GET',
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        },
                        url: AppUtil.restUrls.monkRecipeManagement.getMonkRecipeProfileSheet+"?profile="+$scope.selectedProfile.code
                    }).then(function success(response) {
                        if(response.status === 200 && response.data){
                            var fileName = "MONK_RECIPE_PROFILE_"+$scope.selectedProfile.code + " - " + Date.now() + ".xls";
                            var blob = new Blob(
                                [response.data],
                                {
                                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                }, fileName);
                                saveAs(blob, fileName);
                                $scope.inventoryData=response.data;
                        } else {
                            bootbox.alert("Error getting recipe profiles basic detail.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
    }

    $scope.addVersion = function () {
        var fileExt = getFileExtension(fileService.getFile().name);
        var reader = new FileReader();
        reader.onload = function () {
            for (var i in $scope.profiles) {
                $scope.profiles[i].list=[]
                if ($scope.profiles[i].code == $scope.selectedProfile.code) {
                    $scope.profiles[i].flag = true;
                    $scope.profiles[i].list.push(JSON.parse(csvJSON(reader.result)));
                }
            }
            console.log("regions", $scope.regions);
            $timeout(function () {
                $scope.selectedRegion = {};
                $scope.showUploaded = true;
            });

        }

        if (isCorrectFile(fileExt)) {
            var fd = new FormData();
            fd.append("file", fileService.getFile());
            // console.log(fd.get("file"))
            reader.readAsBinaryString(fd.get("file"))
            angular.element("input[type='file']").val(null);
            fileService.push(null);
            $scope.bulkCSVFile = null;
        } else {
            alert("please upload csv file only");
            angular.element("input[type='file']").val(null);
            fileService.push(null);
            $scope.bulkCSVFile = null;

            return;
        }


    };


    $scope.saveVersions = function () {
        var result = confirm("Are You Sure You Want To Save monk versions for all regions");
        if (!result) {
            return;
        }
        var object = [];
        $scope.profiles.forEach(function (data) {
            if (data.list.length > 0) {
                object.push({
                    rawData: data.list[0],
                    region: data.code,
                    userId: $scope.userDetails.user.id,
                    userName: $scope.userDetails.user.name
                })
            }
        })
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.monkRecipeManagement.saveNewVersions,
            data: object
        }).then(function success(response) {
            console.log(response.data);
            $scope.afterUploadingResponse=response.data;
            $rootScope.showFullScreenLoader = false;
            $scope.afterUploading=true;
            $scope.showUploaded = false;

        }, function error(response) {
            console.log("error", msg);
            alert(msg);
            $rootScope.showFullScreenLoader = false;

        })
    }

    function isCorrectFile(fileExt) {
        return fileExt == "xls" || fileExt == "csv";
    }

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function csvJSON(csv) {
        var lines = csv.split("\n");
        var result = [];
        var headers = lines[0].split(",").map(function(item) {
            return item.trim();
        });

        for (var i = 1; i < lines.length; i++) {
            var obj = {};
            var currentLine = lines[i].split(",").map(function(item) {
                return item.trim().replace(/\r/g, '');
            });
            var count = 0;
            var addonsMap = new Map();
            if (currentLine.length === headers.length) {
                for (var j = 0; j < headers.length; j++) {
                if( j > 5 ){
                    const value = parseInt(currentLine[j]);
                    if(!isNaN(value)){
                        addonsMap.set(headers[j], value);
                    }                }
                else{
                    obj[headers[j]] = currentLine[j];
                    }
                }
                const orderedAddons = {};
                for (let [key, value] of addonsMap.entries()) {
                     orderedAddons[key] = value;
                }

                obj["addons"] = orderedAddons;
                result.push(obj);
            }
        }

        return JSON.stringify(result);
    }

});
