<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    .row-spacing {
        margin-top: 10px;
    }

    .region-card {
        font-size: 20px;
        font-weight: 700;
        color: green;
    }

    .multiselect-parent .dropdown-menu {
        width: 570px;
    }

    .row-selected {
        background-color: darkgray;
    }

    .card-selected {
        background-color: #f0ad4e;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">

    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Developer Dashboard</h2>
    </div>
    <br>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>


    <div class="row" data-ng-if="selectedAction == 'Kettle Cache'">
        <br>
        <div class="col-xs-6">
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear master cache with flush inventory"
                       data-ng-click="clearCache('refreshMasterCacheWithInventory')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear locality cache"
                       data-ng-click="clearCache('refreshLocalityCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear tax cache"
                       data-ng-click="clearCache('refreshTaxCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear recipe cache"
                       data-ng-click="clearCache('refreshRecipeCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear payment cache"
                       data-ng-click="clearCache('refreshPaymentCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear division cache"
                       data-ng-click="clearCache('refreshDivisionCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear department cache"
                       data-ng-click="clearCache('refreshDepartmentCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear designation cache"
                       data-ng-click="clearCache('refreshDesignationCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear tax profile cache"
                       data-ng-click="clearCache('refreshTaxProfileCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear denomination cache"
                       data-ng-click="clearCache('refreshDenominationCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear employee cache"
                       data-ng-click="clearCache('refreshEmployeeCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear hod cache"
                       data-ng-click="clearCache('refreshHodDetailCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear pre authenticated API cache"
                       data-ng-click="clearCache('refreshPreAuthenticatedApiCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear unit cache"
                       data-ng-click="clearCache('refreshUnitCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear unit cache with flush inventory"
                       data-ng-click="clearCache('refreshUnitCacheWithInventory')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear list data cache"
                       data-ng-click="clearCache('refreshListDataCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear addon data cache"
                       data-ng-click="clearCache('refreshAddonDataCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear product cache"
                       data-ng-click="clearCache('refreshProductCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear cancellation cache"
                       data-ng-click="clearCache('refreshCancellationCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear kiosk companies cache"
                       data-ng-click="clearCache('refreshKioskCompanies')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear external API cache"
                       data-ng-click="clearCache('refreshExternalApi')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear environment properties cache"
                       data-ng-click="clearCache('refreshEnvironmentProps')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear locations cache"
                       data-ng-click="clearCache('refreshLocations')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear item per ticket cache"
                       data-ng-click="clearCache('refreshItemPerTicket')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear brand metadata cache"
                       data-ng-click="clearCache('refreshBrandMetadata')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear entity alias cache"
                       data-ng-click="clearCache('refreshEntityAliasData')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear partner brand mapping cache"
                       data-ng-click="clearCache('refreshPartnerBrandMappingData')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear unit menu sequence mapping cache"
                       data-ng-click="clearCache('reloadUnitMenuSequenceMapping')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear unit channel partner mapping cache"
                       data-ng-click="clearCache('reloadUnitChannelPartnerMapping')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear channel partner cache"
                       data-ng-click="clearCache('reloadChannelPartner')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear unit partner brand metadata cache"
                       data-ng-click="clearCache('reloadUnitPartnerBrandMetadata')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear campaign detail cache"
                       data-ng-click="clearCampaignCache()"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Region cache"
                       data-ng-click="clearCache('refreshRegionCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Subscription cache"
                       data-ng-click="clearCache('refreshSubscriptionCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Reference Cache"
                       data-ng-click="clearCache('refreshReferenceMetadataCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Unit Partner Edc Mapping"
                       data-ng-click="clearCache('refreshUnitPartnerEdcMapping')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Source Condiment Mapping"
                       data-ng-click="clearCache('refreshSrcCondimentMapping')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Product to Condiment Mapping"
                       data-ng-click="clearCache('refreshGroupCondimentMapping')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh All Experiments"
                       data-ng-click="clearCache('refreshAllExperiments')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Experiment To Scenario Mappings"
                       data-ng-click="clearCache('refreshExperimentToScenarioMappings')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Scenario To Experiment Mappings"
                       data-ng-click="clearCache('refreshScenarioToExperimentMappings')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Clear Customer Applied Coupon Cache"
                       data-ng-click="clearCache('clearCustomerAppliedCouponCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Monk Recipes"
                       data-ng-click="clearCache('refreshUnitMonkRecipeProfilesVersion')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Work Stations Station Categories"
                       data-ng-click="clearCache('refreshWorkStationsStationCategories')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Price Category wise Product price"
                       data-ng-click="clearCache('refreshPriceCategoryWiseProductsPrice')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Unit Drool Version Mapping"
                       data-ng-click="clearCache('refreshUnitDroolVersionMapping')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Dispenser Canister Cache"
                       data-ng-click="clearCache('refreshDispenserItemCanisterCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Price Profile Products Mappings Cache"
                       data-ng-click="clearCache('refreshPriceProfileProductsMappingCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Company Brands Mapping Cache"
                       data-ng-click="clearCache('refreshCompanyBrandsMappingCache')"/>
            </div>
            <div class="form-group">
                <input type="button" class="btn btn-primary" value="Refresh Unit Brand Mappings Cache"
                       data-ng-click="clearCache('refreshUnitBrandMappingsCache')"/>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group">
                <input type="button" class="btn btn-danger" value="Clear master cache"
                       data-ng-click="clearCache('refreshMasterCache')"/>
            </div>
        </div>
    </div>
</div>

<div class="row" data-ng-if="selectedAction == 'Dinein Cache'">
    <br>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Menu Sequence"
                   data-ng-click="clearDineInCache('allMenuSequence')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Product Data"
                   data-ng-click="clearDineInCache('allproducts')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh List Data"
                   data-ng-click="clearDineInCache('list')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Tax Cache"
                   data-ng-click="clearDineInCache('tax')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh City Cache"
                   data-ng-click="clearDineInCache('locations')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh All Product Images"
                   data-ng-click="clearDineInCache('productImages')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Wallet Products"
                   data-ng-click="clearDineInCache('walletProducts')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Wallet Offers"
                   data-ng-click="clearDineInCache('walletOffers')"/>
        </div>

        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Location Metadata"
                   data-ng-click="clearDineInCache('locationMetadata')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Order State Metadata"
                   data-ng-click="clearDineInCache('orderStateMetadata')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Location Data"
                   data-ng-click="clearDineInCache('locationData')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Unit Operation Hours"
                   data-ng-click="clearDineInCache('unitOperations')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh All Unit Offers"
                   data-ng-click="clearDineInCache('unitOffer')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh All Unit App Blockers"
                   data-ng-click="clearDineInCache('unitAppBlockers')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh All Unit Alliances"
                   data-ng-click="clearDineInCache('unitAlliances')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="refresh All Unit Banners"
                   data-ng-click="clearDineInCache('unitBanners')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Wallet Banners"
                   data-ng-click="clearDineInCache('walletBanners')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh All Promotions"
                   data-ng-click="clearDineInCache('unitPromotions')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Chaayos Giftings"
                   data-ng-click="clearDineInCache('unitChaayosGiftings')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Web Tags"
                   data-ng-click="clearDineInCache('refreshWebTags')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh App Shuffling"
                   data-ng-click="clearDineInCache('appShufflingCategorySequenceData')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Sequences in Category"
                   data-ng-click="clearDineInCache('refreshSequenceData')"/>
        </div>
    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-danger" value="Refresh All"
                   data-ng-click="clearDineInCache('all')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-danger" value="Refresh Units"
                   data-ng-click="clearDineInCacheWithSource('allUnits')"/>
        </div>
    </div>
</div>

<div class="row" data-ng-if="selectedAction == 'Sumo Cache'">
    <br>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Attribute Definition Cache"
                   data-ng-click="clearScmCache('refreshAttributeDefinitionCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Category Definition Cache"
                   data-ng-click="clearScmCache('refreshCategoryDefinitionCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Packaging Definition Cache"
                   data-ng-click="clearScmCache('refreshPackagingDefinitionCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Category Attribute Mapping Cache"
                   data-ng-click="clearScmCache('refreshCategoryAttributeMappingCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Product Definition Cache"
                   data-ng-click="clearScmCache('refreshProductDefinitionCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Sku Definition Cache"
                   data-ng-click="clearScmCache('refreshSkuDefinitionCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Unit Detail Cache"
                   data-ng-click="clearScmCache('refreshUnitDetailCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Unit Category Cache"
                   data-ng-click="clearScmCache('refreshUnitCategoryCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Attribute Value Cache"
                   data-ng-click="clearScmCache('refreshAttributeValueCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Category Attribute Value Cache"
                   data-ng-click="clearScmCache('refreshCategoryAttributeValueCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Product Packaging Mapping Cache"
                   data-ng-click="clearScmCache('refreshProductPackagingMappingCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Category Packaging Mapping Cache"
                   data-ng-click="clearScmCache('refreshCategoryPackagingMappingCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Sku Attribute Value Cache"
                   data-ng-click="clearScmCache('refreshSkuAttributeValueCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Vendor Detail Cache"
                   data-ng-click="clearScmCache('refreshVendorDetailCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Sub Category Definition Cache"
                   data-ng-click="clearScmCache('refreshSubCategoryDefinitionCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Production Line Cache"
                   data-ng-click="clearScmCache('clearProductionLineCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Sku Definition List  Cache"
                   data-ng-click="clearScmCache('clearSkuDefinitionListCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Region Fulfillment Mapping  Cache"
                   data-ng-click="clearScmCache('refreshRegionMappingCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Pending Milk Bread Cache"
                   data-ng-click="clearScmCache('pendingMilkBread')"/>
        </div>
        <div class="form-group" data-ng-if="currentUserId == 140199">
            <div class="col s6">
                <div class="col s3" ng-dropdown-multiselect="" extra-settings="multiSelectSettingsMilkBread"
                     options="allUnitsList" selected-model="selectedMilkBreadUnits">
                </div>
                <div class="col s3">
                    <input type="button" class="btn btn-danger" value="Mark milk Bread Completed"
                           data-ng-if="selectedMilkBreadUnits.length > 0" data-ng-click="markMilkBreadCompleteForUnits()"/>
                </div>
            </div>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Asset Cache"
                   data-ng-click="clearScmCache('refreshAssetCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Fulfillment Unit Mapping Cache"
                   data-ng-click="clearScmCache('refreshFulfillmentUnitMappingCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Refresh Skus by productId cache"
                   data-ng-click="clearScmCache('refreshSkusByProductIdCache')"/>
        </div>
    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-danger" value="Clear Complete Cache"
                   data-ng-click="clearScmCache('refreshCompleteCache')"/>
        </div>
    </div>
</div>


<div class="row" data-ng-if="selectedAction == 'Neo Cache'">
    <br>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Unit Cache"
                   data-ng-click="clearNeoCache('refreshUnitCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Location Cache"
                   data-ng-click="clearNeoCache('refreshLocationCache')"/>
        </div>
    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-danger" value="Clear Complete Cache"
                   data-ng-click="clearNeoCache('refreshCompleteCache')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-danger" value="Clear Complete Redis Cache"
                   data-ng-click="clearNeoCache('refreshCompleteRedisCache')"/>
        </div>
    </div>
</div>

<div class="row" data-ng-if="selectedAction == 'Kettle2 Cache'">
    <br>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Unit Cache"
                   data-ng-click="clearKettle2Cache('refreshAllUnit')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Payment Mode Cache"
                   data-ng-click="clearKettle2Cache('refreshAllPaymentMode')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Complimentary Code Cache"
                   data-ng-click="clearKettle2Cache('refreshAllComplimentaryCode')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Cancellation Reason Cache"
                   data-ng-click="clearKettle2Cache('refreshAllCancellationReason')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Product Category Cache"
                   data-ng-click="clearKettle2Cache('refreshProductCategory')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Product Image Cache"
                   data-ng-click="clearKettle2Cache('refreshAllProductImage')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear Menu Sequence Cache"
                   data-ng-click="clearKettle2Cache('refreshMenuSequence')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear All Unit Menu Sequence Cache"
                   data-ng-click="clearKettle2CacheWithSource('refreshAllUnitMenuSequence')"/>
        </div>
        <div class="form-group">
            <input type="button" class="btn btn-primary" value="Clear All Unit Product Dynamic Price Cache"
                   data-ng-click="clearKettle2CacheWithSource('refreshAllUnitProductDynamicPrice')"/>
        </div>
        <div>
            <input type="button" class="btn btn-primary" value="Clear AB Testing Cache"
                   data-ng-click="clearKettle2Cache('clearABTestingCache')"/>
        </div>
        <div>
            <input type="button" class="btn btn-primary" value="Clear Product Recommendation Cache"
                   data-ng-click="clearKettle2Cache('clearRecommendationCache')"/>
        </div>
        <div>
            <input type="button" class="btn btn-primary" value="Clear Dispenser Cache"
                   data-ng-click="clearKettle2Cache('clearDispenserCache')"/>
        </div>    </div>
    <div class="col-xs-6">
        <div class="form-group">
            <input type="button" class="btn btn-danger" value="Clear Complete Cache"
                   data-ng-click="clearKettle2CacheWithSource('refreshDataHubMasterCache')"/>
        </div>
    </div>
</div>
