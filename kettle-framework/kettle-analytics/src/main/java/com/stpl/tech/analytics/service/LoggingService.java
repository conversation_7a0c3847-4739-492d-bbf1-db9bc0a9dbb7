package com.stpl.tech.analytics.service;

import com.google.gson.Gson;
import com.stpl.tech.analytics.model.MonkDashboardData;
import com.stpl.tech.analytics.model.WeightCalibrationDetail;
import com.stpl.tech.analytics.model.chaimonk.log.MonkFileUploadType;
import com.stpl.tech.kettle.data.model.MonkLogData;
import com.stpl.tech.kettle.domain.model.MonkTaskCompletionStatsDto;
import com.stpl.tech.kettle.domain.model.MonkXTwoFailureLogsDTO;
import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyEventLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyOrderLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.GenericLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkBrokerStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkDiagnosisLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLogNew;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkXTwoLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.NotificationData;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public interface LoggingService {

	public boolean addAssemblyLog(AssemblyEventLog data);

	public boolean addMonkLogData(MonkLogData logData);

    public boolean addMonkConsumptionLogData(List<MonkConsumptionLog> consumptionLogList);

    boolean addMonkConsumptionLogData(MonkConsumptionLog consumptionLogList);

    public boolean addMonkStatusLogData(List<MonkStatusLog> statusLogList);

    boolean addMonkStatusLogData(MonkStatusLog statusLog);

    public boolean addMonkTaskStatusLogData(List<MonkTaskStatusLog> taskStatusLogList);

    boolean addMonkTaskStatusLogData(MonkTaskStatusLog taskStatusLog);

    public boolean addMonkTaskStatusDetailLogData(List<MonkTaskStatusDetailLog> detailLogs);

    public boolean addAssemblyOrderLog(List<AssemblyOrderLog> taskLogList);

    public void uploadLogFile(MultipartFile file, Integer unitId, MonkFileUploadType fileType);

    public boolean uploadMonkBrokerLogs(MultipartFile file, Integer unitId);

    boolean addMonkTaskStatusDetailLogData(MonkTaskStatusDetailLog detailLog);

    public boolean addMonkTaskStatusDetailLogNewData(List<MonkTaskStatusDetailLogNew> detailLogs);

    List<Object> queryGeneric(String queryString, int limit,int page, Class cls);

    boolean addMonkDiagnosisLogData(MonkDiagnosisLogData monkDiagnosisLogData);

    public boolean addWeightCalibrationDetail(WeightCalibrationDetail weightCalibrationDetail);

    public Map<String, WeightCalibrationDetail> getWtCalibrationDetail(Integer unitId);

    Map<Integer, MonkDashboardData> getRealTimeDashboardDetails(Integer orderId);

    void saveRealTimeDashboardDetails(MonkStatusLog log);

    boolean savePreAllocationLogs(Integer unitId, Date businessDate);

    void saveMonkTaskCompletionStats(MonkStatusLog log);

    void saveMonkTaskCompletionStatLogs(MonkTaskCompletionStatsDto log);

    boolean sendNotificationToAndroid(NotificationData notificationData);

    boolean saveNotificationToAndroid(Integer keyId, String responseMessage);

    void sendGChatMessageOfError(String data);

    void markAssemblyNotificationsAsNotDelivered();

    void saveMonkBrokerStatusLog(MonkBrokerStatusLog log);

    void addGenericLogData(GenericLogData log, Gson gson);

    void addMonkXTwoLog(MonkXTwoLog log);
    
    void sendGChatMessageOfChaiMonk2Error(String data);
    
    void addMonkXTwoFailureLogs(MonkXTwoFailureLogsDTO log);
}
