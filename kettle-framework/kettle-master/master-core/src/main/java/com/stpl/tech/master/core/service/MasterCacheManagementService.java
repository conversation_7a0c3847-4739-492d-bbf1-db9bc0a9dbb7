package com.stpl.tech.master.core.service;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.DispenserCanisterItemDataDto;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.locality.model.LocalityMapping;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * Created by Chaayos on 09-07-2016.
 */
public interface MasterCacheManagementService {

    public boolean refreshPreAuthenticatedApiCache();

    public boolean refreshMasterCache(boolean flushInventory) throws DataNotFoundException;

    public boolean refreshUnitCache(boolean flushInventory) throws DataNotFoundException;

    public boolean refreshProductCache() throws DataNotFoundException;

    public boolean refreshRecipeCache() throws DataNotFoundException;

    public boolean addRecipeIntoCache(Integer id);

    public boolean refreshCancellationReason() throws DataNotFoundException;

    public boolean refreshPaymentCache() throws DataNotFoundException;


    public boolean refreshDivisionCache() throws DataNotFoundException;

    public boolean refreshDepartmentCache() throws DataNotFoundException;

    public boolean refreshDesignationCache() throws DataNotFoundException;

    public boolean refreshTaxProfileCache() throws DataNotFoundException;

    public boolean refreshDenominationCache() throws DataNotFoundException;

    public boolean refreshEmployeeCache() throws DataNotFoundException;
    public boolean refreshHodDetailCache() throws DataNotFoundException;

    public boolean refreshListDataCache() throws DataNotFoundException;

    public boolean refreshAddonDataCache() throws DataNotFoundException;

    public boolean refreshKioskCompanyCache() throws DataNotFoundException;

    public boolean refreshExternalAPICacheCache() throws DataNotFoundException;

    public boolean uploadLocalities(List<LocalityMapping> localities);

    public boolean uploadSingleLocalities(LocalityMapping localities);

    public boolean updateLocality(LocalityMapping localities);

    boolean refreshUnitPartnerMenuMappings();

    boolean reloadUnitChannelPartnerMappings();

    boolean reloadChannelPartner() throws DataNotFoundException;

    public boolean uploadLocalities(String fileName);

    public View downloadLocalities();

    public Set<IdCodeName> getUnitCityList();

    public Set<IdCodeName> getUnitsOfLocation(int locationId);

    public boolean refreshLocalityCache();

    List<LocalityMapping> getLocalities();

    public boolean refreshEnvironmentPropsCache();

    public View getMasterLocalityView();

    public void uploadLocalities(MultipartFile file) throws IOException;

    public void refreshLocation();

    public boolean refreshItemPerTicket() throws DataNotFoundException;

    public boolean refreshBrandMetaData();

    public boolean refreshExpenseMetaData();

    public boolean refreshUnitPartnerBrandMappingData();

    public boolean refreshEntityAliasMappingData();

    void refreshUnitPartnerBrandMetadata();

    public void refreshRegions();

    void refreshCacheReferenceValue();

    void refreshUnitPartnerEdcMappings() throws DataNotFoundException;

    void refreshSourceCondimentMapping() throws  DataNotFoundException;

    void refreshUnitClosureStateMetadata() throws  DataNotFoundException;

    void refreshGroupCondimentMapping() throws  DataNotFoundException;

    void refreshUnitIpAddressCache() throws  DataNotFoundException;
    void clearCustomerAppliedCouponCache();

    void refreshUnitMonkRecipeProfilesVersion();

    public IMap<Integer, Pair<String, String>> getUnitMonkRecipeProfilesVersion();

    void refreshWorkStationsStationCategories();
    void refreshPriceCategoryWiseProductsPrice();

    public boolean refreshUnitDroolVersionMappingCache();

    public void refreshSpecialMilkVariantCache();

    void refreshDispenserItemCanisterCache();
    public void refreshPriceProfileProductMapping();

    IMap<Integer, DispenserCanisterItemDataDto> getDispenserItemCanisterCache();

    public void refreshCompanyBrandsMappingCache();

    public void refreshUnitBrandMappingsCache();

}
