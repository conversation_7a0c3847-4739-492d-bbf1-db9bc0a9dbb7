/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.stpl.tech.kettle.report.metadata.model.WarningMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.CacheReferenceMetadata;
import com.stpl.tech.master.data.model.CategoryAttributes;
import com.stpl.tech.master.data.model.DispenserCanisterItemData;
import com.stpl.tech.master.data.model.DispenserPattiSugarShotInfoData;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.master.data.model.HodDetail;
import com.stpl.tech.master.data.model.PaymentModeAttributes;
import com.stpl.tech.master.data.model.ProductCondimentGroup;
import com.stpl.tech.master.data.model.ProductCondimentItem;
import com.stpl.tech.master.data.model.RegionMap;
import com.stpl.tech.master.data.model.UnitIpAddressData;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.domain.model.AddLocationRequest;
import com.stpl.tech.master.domain.model.AddonList;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.CashMetadata;
import com.stpl.tech.master.domain.model.CategoryAttributesDomain;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.PattiSugarType;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.PreAuthApi;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.TrimmedProductData;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import com.stpl.tech.master.readonly.domain.model.UnitProductRecipesKeys;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Interface for accessing all metadata related to POS. This is the read only
 * API for the metadata.
 *
 * <AUTHOR>
 */
public interface MasterMetadataService {

    public Unit getUnit(int unitId, boolean getAll) throws DataNotFoundException;

    public Collection<Product> getUnitProducts(int unitId, boolean getAll) throws DataNotFoundException;

    Collection<Product> getAllUnitProducts(int unitId, boolean getAll, boolean getRecipes) throws DataNotFoundException;

    public Collection<IdCodeName> getUnitProductsTrimmed(int unitId, boolean getAll) throws DataNotFoundException;

    public TransactionMetadata getTransactionData(boolean isAndroid) throws DataNotFoundException;

    public TransactionMetadata getMetadataCategories() throws DataNotFoundException;

    public List<Unit> getAllUnits() throws DataNotFoundException;

    public List<Unit> getAllUnits(UnitCategory category) throws DataNotFoundException;

    public List<Division> getAllDivisions() throws DataNotFoundException;

    public List<Department> getAllDepartments() throws DataNotFoundException;

    public List<Address> getAllAddress() throws DataNotFoundException;

    public Boolean saveAuditLog(Integer keyId, String keyType, Integer changedBy, Object newObject, String changeType) throws DataUpdationException, DataNotFoundException;

    boolean validUnitProductRecipes(UnitProductRecipesKeys request);

    public List<ListData> getAllListData(String group, boolean getAll) throws DataNotFoundException;

    public ListData getListData(String code, boolean getAll) throws DataNotFoundException;

    public List<TaxProfile> getAllTaxProfile() throws DataNotFoundException;

    public List<PaymentMode> getAllPaymentMode(PaymentCategory category) throws DataNotFoundException;

    public Map<Integer, BigDecimal> getAllPaymentModeCommisson() throws DataNotFoundException;

    public List<PaymentModeAttributes> getPaymentModeAttributes(int paymentModeId) throws DataNotFoundException;

    public ListData getDiscountCodes(boolean getAll) throws DataNotFoundException;

    public List<AddonList> getAllAddons(boolean getAll) throws DataNotFoundException;

    public List<ListData> getAllCategories(boolean getAll) throws DataNotFoundException;

    public List<Product> getAllProducts(Integer brandId) throws DataNotFoundException;

    public List<Designation> getAllDesignations() throws DataNotFoundException;

    public Map<String, List<ListData>> getAllListData() throws DataNotFoundException;

    public ListData upsertRefLookUp(ListData listData) throws DataUpdationException, DataNotFoundException;

    public List<DenominationDetail> getAllDenominations() throws DataNotFoundException;

    public List<Employee> getAllEmployees() throws DataNotFoundException;

    public List<HodDetail> getAllHods() throws DataNotFoundException;

    Unit updateCacheForManager(Unit newUnit) throws DataNotFoundException;

    public List<PreAuthApi> getPreAuthenticatedApis();

    public List<KioskCompanyDetails> getAllKioskCompanies();

    public List<KioskMachine> getAllKioskMachines();

    public List<KioskLocationDetails> getAllKioskLocations();

    public List<Location> getAllLocations();

    public List<State> getAllStates();

    public List<CancellationReason> getAllCancellationReasons();

    public List<Company> getAllCompanies();

    public List<ExpenseMetadata> getExpenseList(String type);

    public List<ExpenseMetadata> getAllExpenseList();

    public List<WarningMetadata> getWarningReasonList(String type);

    List<PaymentMode> getPaymentModes();

    public List<Unit> getUnitsOfAreaManager(Integer amId);

    List<Unit> getUnitsOfCafeManager(Integer amId);

    public List<ExternalPartnerDetail> getExternalPartnerDetail(String status);

    List<CacheReferenceMetadata> getCacheReferenceMetadata(String status);

    List<UnitToPartnerEdcMapping> getUnitPartnerEdcMappingMetadata(String status);

    List<UnitIpAddressData> getAllUnitIpAddressData();

    public List<CashMetadata> getCashMetadata();

    public Long getAllUnitCount() throws DataNotFoundException;

    public List<Unit> getAllUnits(int start, int batchSize) throws DataNotFoundException;

    public boolean updateHandOverDateForUnit(String handOverData, Integer unitId) throws ParseException;

	public Collection<TrimmedProductVO> getAllUnitProductsPrices(int unitId);

    public Collection<TrimmedProductVO> getAllUnitProductsPricesV1(int unitId);

    Location addLocation(AddLocationRequest locationDetail);

    public List<RegionMap> getAllRegions();

    List<TrimmedProductData> getUnitProductDimensions() throws DataNotFoundException;

    List<ProductCondimentGroup> getAllProductCondimentGroup(String status) throws DataNotFoundException;

    List<ProductCondimentItem> getAllProductCondimentItem(String status) throws  DataNotFoundException;

    public List<String> getDelayReason() throws DataNotFoundException;

    public Map<Integer, UnitContactDetails> getAllUnitContactDetails() throws DataNotFoundException;

    public List<String> getCompensationReason() throws DataNotFoundException;

    public List<CategoryAttributes> getCategoryAttributes();
    public Boolean updateCategoryAttributes(Map<Integer, CategoryAttributesDomain> categoryAttributes, Boolean removeSequencing);

    public Boolean uploadCategoryImage(Map<Integer, CategoryAttributesDomain> categoryImages) throws Exception;

    List<DispenserCanisterItemData> getDispenserCanisterItemData();

    List<DispenserPattiSugarShotInfoData> getDispenserPattiSugarShotInfoData(PattiSugarType pattiSugarType);

    Map<Integer, Map<Integer, List<IdCodeName>>> allProducts();

    Map<String, List<ListData>> getListTypesByType(List<String> refTypes) throws DataNotFoundException;

    public Map<Integer, List<Brand>> getCompanyBrandsMapping();

    List<Product> getAllProductsFromDao() throws DataNotFoundException;
}
