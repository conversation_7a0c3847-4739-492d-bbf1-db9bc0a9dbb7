/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.kettle.report.metadata.model.NameValue;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.data.model.*;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ImageCategoryType;
import com.stpl.tech.master.domain.model.MachineProductMetaDataResponse;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductCheckList;
import com.stpl.tech.master.domain.model.ProductCityImageMappingDomain;
import com.stpl.tech.master.domain.model.ProductImageMappingDetail;
import com.stpl.tech.master.domain.model.ProductImageMappingDetailList;
import com.stpl.tech.master.domain.model.ProductPriceProfile;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitProductMappingData;
import com.stpl.tech.master.domain.model.ProductRecipeMappingRequest;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ProductManagementService {

	public Product addProduct(Product product) throws DataUpdationException, DataNotFoundException;

    public Product updateProduct(Product product) throws DataNotFoundException;

    public boolean changeProductStatus(int productId, ProductStatus status) throws DataNotFoundException;

	public List<UnitProductMappingData> getUnitProductPrice(UnitCategory unitCategory, String unitRegion, int productId,
			int dimensionId);
	public List<UnitProductMappingData> getUnitProductPrice(String unitCategory, Integer productId,
                                                            Integer dimensionId, String pricingProfileIds, String locationIds,
                                                            String region, Integer companyId);

    Map<Integer, Map<Integer, List<ProductRecipeKey>>> getUnitProductPriceProfiles();

    public Set<Integer> updateUnitProductPrice(List<UnitProductMappingData> mappings, Integer updatedBy) throws DataNotFoundException;

    public boolean updateUnitProductPrice(int unitId, int productId, String dimensionCode, BigDecimal price, String profile);

    public boolean updateUnitProductMappingStatus(int unitId, int productId, ProductStatus status);

    public void refreshUnit(int unitId) throws DataNotFoundException;

    public List<ProductPriceProfile> getProductProfileUnits(Integer productId, String profile);

    public ProductDescription saveProductDescriptionData(ProductDescription productDescription);

    ProductDescription getProductDescriptionData(int productId);

    List<ProductDescription> getProductDescriptionDataList(List<Integer> list);

    ProductImageMappingDetail saveProductImage(MimeType mimeType, int productId, ImageCategoryType imageCategoryType, MultipartFile file, int updatedBy, Integer index, String link);

    ProductImageMappingDetail saveProductDimensionImage(MimeType mimeType, int productId, ImageCategoryType imageCategoryType, MultipartFile file, int updatedBy, Integer index, String link,Integer dimensionCode);

    ProductImageMappingDetail getProductImages(int productId);

    ProductImageMappingDetail getProductDimensionImages(int productId,Integer dimensionCode);

    List<NameValue> getImageCategoryType();

    ProductImageMappingDetailList getAllProductImages();

    ProductImageMappingDetailList getAllProductImages(List<Integer> productIdList);

    List<IdCodeName> getUnitDetails(UnitCategory unitCategory, String unitRegion);

    List<Integer> findDistinctRecipeIdInMenuToSCMProductMap();

    void saveMenuToSCMMapData(List<MenuToSCMProductMapData> menuToSCMProductMapData);

    List<ProductImageMapping> getProductImageDetail(List<Integer> list);

    List<ProductNutritionDetail> getProductsNutritionInfo(List<Integer> productIds);

    List<IdCodeName> recipeProfileInactivationCheck(Integer productId, Integer dimensionId, String profile);

    public void truncateMenuToScmTable();

    public List<Integer> getUnitsByRecipeProfileAndProductId(String profile , Integer productId);

    Map<Integer, List<ProductRecipeKey>> getProductPriceProfilesForUnit(int unitId);

    Map<String,Map<Integer,Map<NutritionDataEnum,BigDecimal>>> getProductsNutritionData(List<String> sourceType);
    Boolean updateProductsNutritionInfo(Map<Integer,Map<NutritionDataEnum,BigDecimal>> data , String sourceType);

    ProductImageMappingDetail saveProductImageByCity(MimeType mimeType, int productId,
                                                            MultipartFile file, int updatedBy, String cityName ,
                                                            String link,String daySlot);

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    ProductImageMappingDetailList getAllProductDimensionImages(List<Integer> productIdList);

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    Map<String, List<ProductImageMappingDTO>> getProductDimensionImageData();

    Map<Integer,Map<String, ProductCityImageMappingDomain>> getAllProductImagesByCity(List<Integer> productIdList , String cityName);

    Map<Integer,Map<String, ProductCityImageMappingDomain>> getAllProductImagesByCity(String cityName, String daySlot);

    ProductCityImageMappingDomain getProductImagesByCity(Integer productId , String cityName, String daySlot);


    public Map< String,Map<Integer,  Set<UnitProductPriceCategoryDomain>>> getUnitsSpecificProductsPrice();

    Boolean updateUnitProductProfile(List<UnitProductMappingData> mappings, Integer updatedBy);

    public Boolean deactivateImageByCity(int productId,int updatedBy, String cityName, String daySlot);

    public void createProductCheckList(List<ProductCheckList> productCheckList, ProductCheckListEvent checkListEvent);

    public ProductCheckListEvent createCheckListEvent(Integer userId);

    public ProductCheckListEvent getProductCheckListEvent(Integer eventId);

    public List<MachineProductMetaDataResponse> getMachineProductMappingData(Integer unitId, Integer brandId);

    ApiResponse getUnitProductMapping(ProductRecipeMappingRequest request) throws MasterException;

    void changeUnitProductProfile(UnitProductProfileContext context) throws MasterException;

    void sendFailureEmail(String subject, String body);

    void bulkUpdateUnitProductProfile(UnitProductProfileContext context) throws MasterException, DocumentException, IOException;

    void getUnitProductMappingToEmail(ProductRecipeMappingRequest request) throws Exception;

    ApiResponse getUnitsShortByBrandId(Integer brandId);

}

