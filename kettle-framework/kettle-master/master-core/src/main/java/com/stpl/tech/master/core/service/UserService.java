/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.Department;
import com.stpl.tech.master.data.model.Designation;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.DocUploadTypeDTO;
import com.stpl.tech.master.domain.model.DocumentDetailDTO;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EmployeeApplicationMapping;
import com.stpl.tech.master.domain.model.EmployeeRole;
import com.stpl.tech.master.domain.model.EmployeeRoleUpdateRequest;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.master.domain.model.FileTypeDTO;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UserPolicyDataDTO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Interface for accessing all metadata related to POS. This is the read only
 * API for the metadata.
 *
 * <AUTHOR>
 *
 */
public interface UserService {

	public String authenticateUser(UserSessionDetail userSession, String ipAddress,
								   String macAddress, String userAgent, boolean isAdmin) throws AuthenticationFailureException;

	public boolean changePasscode(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent)
			throws AuthenticationFailureException;

	public Map<String, Map<String, Boolean>> getAcl(ApplicationName appName, int employeeId, Integer companyId, Integer brandId);

	public boolean resetPasscode(int userId, String newPasscode, int updatedBy) throws AuthenticationFailureException;

	public boolean logout(int unitId, int userId, String sessionKey)
			throws AuthenticationFailureException;

	public List<Employee> getEmployees(int unitId) throws DataNotFoundException;

	public Employee getEmployee(int userId);

	public Employee getEmployee(String empCode);

	public EmployeeBasicDetail getActiveEmployee(int userId);

	public Boolean authorizeMac(String macAddress);

	public Set<String> getAuthorizedMacs();

	public Employee addEmployee(Employee employee,Department department,Designation designation) throws DataUpdationException;

	public Employee updateEmployee(Employee employee) throws DataUpdationException;

	public boolean updateStatus(int employeeId, EmploymentStatus status) throws DataUpdationException;

	public boolean addEmployeeUnitMapping(int employeeId, List<Integer> unitIds) throws DataUpdationException;

	public List<UnitBasicDetail> getUnitsForEmployee(int employeeId, boolean onlyActive) throws DataNotFoundException;

	public Designation getDesignation(int designationId);

	public Designation getDesignation(String designationName);

	public Department getDepartment(int deptId);

	public Department getDepartment(String deptName);

	public boolean createPasscode(int userId, String newPasscode) throws AuthenticationFailureException;

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    List<EmployeeBasicDetail> getActiveEmployees(String designation, String department);

    public List<EmployeeBasicDetail> getAllEmployees(String designation);

	public List<EmployeeBasicDetail> getAllEmployees();

	public List<EmployeeBasicDetail> getEmployeesForUnit(Integer unitId);

	public List<IdCodeName> getEmployeesForUnitTrimmed(Integer unitId);

	public boolean verifyUser(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent) throws AuthenticationFailureException;

	public EmployeeBasicDetail getEmployeeBasicDetail(int userId);

	public EmployeeBasicDetail getEmployeeBasicDetail(String empCode);

	public List<IdName> getActiveEmployeeIdName();

	/**
	 * @param roles
	 * @return
	 */
	public boolean updateRoles(EmployeeRole roles);

	//public Map<String, Map<String, Integer>> setPermissionCache(int employeeId);

	void updateEmployeeRoles(Integer employeeId, Integer updatedBy, Integer policyId, List<Integer> overrideRoles);

	List<IdCodeName> getActiveRoles();

	/**
	 * @param employeeId
	 * @return
	 */
	List<IdCodeName> getActiveRoles(int employeeId);

	public List<Integer> getActiveEmpUnitIds(int employeeId);

	public List<EmployeeBasicDetail> getEmployeesForEmployeeMealForUnit(int unitId);

	public Employee getEmployeeBySdpContact(String sdpContact);

	/**
	 * @param userId
	 * @return
	 */
	public String getEmplyeePassCode(int userId);

    public List<IdCodeName> getPurchaseRoles(int empId);

    public Map<Integer, String> getEmployeeWithRole(String role);

	View getEmployeeOnboardingView();

	boolean uploadEmployeeOnboardingSheet(MultipartFile file) throws Exception;

	Integer getEmployeeIdByEmail(String userEmail);

    Boolean addEmployeeAppMapping(EmployeeApplicationMapping employeeMapping) throws Exception;

	Boolean updateEmployeeAppMapping(EmployeeApplicationMapping employeeMapping);

	EmployeeApplicationMapping getEmpAppMapping(int employeeId);

	Boolean verifyUserForOrderCancellation(Integer empId ,String passcode);

	String getLastActivityTime(String system1, String system2);

	Boolean addLastActivityTime(String system1, String system2, String date);

	Boolean updateEmpData(Employee employeeDetail,Department department,Designation designation);

	Integer lookUpDepartmentDesignation(com.stpl.tech.master.domain.model.Department department, String designationName);

	Boolean addDepartmentDesignationMapping(Integer deptId , Integer designationId);

	BusinessDivision getBusinessDivision(Integer businessDivId);

    void inValidateSessionCache(List<Integer> unitId);

	void setEmployeeInActive(String empCode,String empContact);

	Integer getPosVersionEnabledForUnit(Integer unitId);

	String getFaDaycloseEnabledForUnit(Integer unitId);

    DocumentDetailDTO uploadDocument(FileTypeDTO type, MimeType mimeType, DocUploadTypeDTO docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException;

    List<UserPolicyDataDTO> getUserPolicies();

    boolean createUpdateUserRolesForPolicy(EmployeeRole employeeRole, Integer uploadedDocId, String departmentDesignation) throws DataUpdationException;

	void logRoleChangeAudit(Integer userPolicyEmployeeId, String type, Integer updatedBy, Integer uploadedDocId);

	List<EmployeeBasicDetail> getEmployeesWithDepartmentDesignation(Integer departmentId, Integer designationId);

    boolean resetUpdateEmployeeUserPolicy(Integer employeeId, Integer policyId, Integer updatedBy, Integer uploadedDocId);

	Integer getUnitCafeManager(Integer unitId);
	Date getLastHandoverDate(Integer unitId);
	String getLastHandoverFrom(Integer unitId);

	boolean updateHod(Integer empId, Integer loggedInUser) throws MasterException;

	Boolean updateRolesV2(EmployeeRoleUpdateRequest request);

}
