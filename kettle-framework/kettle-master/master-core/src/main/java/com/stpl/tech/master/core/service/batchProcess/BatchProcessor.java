package com.stpl.tech.master.core.service.batchProcess;

import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.service.DataSourceResolver;
import com.stpl.tech.master.core.service.batchProcess.batchAnnotation.BatchProcess;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Slf4j
@Component
public class BatchProcessor {

    @Autowired
    private DataSourceResolver dataSourceResolver;

    private static final Integer DEFAULT_BATCH_SIZE = 100;



    /**
     * Batch insert method to insert a list of entries into the database.
     *
     * @param entries   List of entries to be inserted
     * @param batchSize Size of each batch for processing
     * @throws MasterException if any error occurs during the batch insert
     */
    @SneakyThrows
    public <T> void batchInsert(List<T> entries, Integer batchSize) {
        if (CollectionUtils.isEmpty(entries)) {
            log.warn("----  Attempted to Batch Insert with empty list  ----");
            return;
        }

        Class<T> entityClass = (Class<T>) entries.get(0).getClass();

        BatchProcessQueryGenerator<T> queryGenerator = new BatchProcessQueryGenerator<>(entityClass);
        String insertQuery = queryGenerator.generateInsertQuery();

        // Perform batch insert
        performBatchInsert(entries, insertQuery, queryGenerator, batchSize);

    }

    private <T> void performBatchInsert(List<T> entries, String insertQuery, BatchProcessQueryGenerator<T> queryGenerator, Integer batchSize) throws MasterException {
        BatchProcess uploadConfig = entries.get(0).getClass().getAnnotation(BatchProcess.class);

        if(batchSize == null || batchSize <= 0) {
            if(uploadConfig != null) {
                batchSize = uploadConfig.batchSize();
            } else {
                batchSize = DEFAULT_BATCH_SIZE;
            }
        }

        JdbcTemplate jdbcTemplate = dataSourceResolver.resolveCurrent();

        // Split into batches
        for (int i = 0; i < entries.size(); i += batchSize) {
            int end = Math.min(i + batchSize, entries.size());
            List<T> batch = entries.subList(i, end);

            // Build multi-row insert query
            int paramCount = queryGenerator.getColumnNames().size();
            String placeholders = IntStream.range(0, paramCount)
                    .mapToObj(j -> "?")
                    .collect(Collectors.joining(", "));

            String fullQuery = insertQuery + " VALUES " +
                    IntStream.range(0, batch.size())
                            .mapToObj(j -> "(" + placeholders + ")")
                            .collect(Collectors.joining(", "));

            List<Object> params = new ArrayList<>();
            for (T entity : batch) {
                params.addAll(queryGenerator.mapEntityToStatement(entity));
            }

            try {
                log.info("Insert query: {}", fullQuery);
                jdbcTemplate.update(fullQuery, params.toArray());
            } catch (Exception e) {
                log.error("Bulk insert failed at batch starting index {}: {}", i, e.getMessage(), e);
                throw new MasterException("Bulk insert failed", e);
            }
        }
        log.info("Successfully inserted {} entries", entries.size());
    }



    /**
     * Batch update method to update a list of entries in the database.
     *
     * @param entries          List of entries to be updated
     * @param columnsToUpdate  Set of columns to be updated , if there were any specific columns
     * @param batchSize        Size of each batch for processing
     * @throws MasterException if any error occurs during the batch update
     */

    // TODO --> need to make it update in batch...
    @SneakyThrows
    public <T> void batchUpdate(List<T> entries, EnumMap<BatchProcessQueryGenerator.Condition, Set<String>> columnsToUpdate, Integer batchSize) {
        if (CollectionUtils.isEmpty(entries)) {
            log.warn("----  Attempted to Batch update with empty list  ----");
            return;
        }
        Class<T> entityClass = (Class<T>) entries.get(0).getClass();
        BatchProcessQueryGenerator<T> queryGenerator = new BatchProcessQueryGenerator<>(entityClass);
        String updateQuery = queryGenerator.generateUpdateQuery(columnsToUpdate);

        BatchProcess config = entityClass.getAnnotation(BatchProcess.class);
        if(batchSize == null || batchSize <= 0) {
            if(config != null) {
                batchSize = config.batchSize();
            } else {
                batchSize = DEFAULT_BATCH_SIZE;
            }
        }

        JdbcTemplate jdbcTemplate = dataSourceResolver.resolveCurrent();

        for (int i = 0; i < entries.size(); i += batchSize) {
            int end = Math.min(i + batchSize, entries.size());
            List<T> batch = entries.subList(i, end);

            try {
                jdbcTemplate.batchUpdate(updateQuery, new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int j) throws SQLException {
                        T entity = batch.get(j);
                        try {
                            queryGenerator.mapEntityToStatementForUpdate(entity, ps, columnsToUpdate);
                        } catch (MasterException e) {
                            throw new RuntimeException(e);
                        }
                    }

                    @Override
                    public int getBatchSize() {
                        return batch.size();
                    }
                });
            } catch (Exception e) {
                log.error("Batch update failed for batch starting at index {}", i, e);
                throw new MasterException("Batch update failed", e);
            }
        }
        log.info("Successfully updated {} entries", entries.size());
    }


}
