package com.stpl.tech.master.core.service.impl;

import com.google.common.collect.Lists;
import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.PreAuthenticatedApiCache;
import com.stpl.tech.master.core.external.partner.service.ExternalAPIService;
import com.stpl.tech.master.core.service.MasterCacheManagementService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.data.dao.LocalityMappingDao;
import com.stpl.tech.master.data.dao.MasterCacheManagementDao;
import com.stpl.tech.master.data.model.PreAuthenticatedApiData;
import com.stpl.tech.master.domain.model.DispenserCanisterItemDataDto;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.locality.model.LocalityMapping;
import com.stpl.tech.master.locality.model.LocalityWrapper;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 09-07-2016.
 */
@Service
public class MasterCacheManagementServiceImpl implements MasterCacheManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(MasterCacheManagementServiceImpl.class);

    @Autowired
    private MasterCacheManagementDao masterCacheManagementDao;

    @Autowired
    private PreAuthenticatedApiCache preAuthenticatedApiCache;

    @Autowired
    private MasterDataCacheService masterDataCacheService;

    @Autowired
    private ExternalAPIService externalAPIService;

    @Autowired
    private LocalityMappingDao localityMappingDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshLocalityCache() {
        masterDataCacheService.refreshLocalities();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshMasterCache(boolean flushInventory) throws DataNotFoundException {
        masterDataCacheService.loadCache(flushInventory);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshRecipeCache() {
        masterDataCacheService.refreshRecipeCache();
        return true;
    }

    @Override
    public boolean addRecipeIntoCache(Integer id){
        return masterDataCacheService.addRecipeIntoCache(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshPaymentCache() throws DataNotFoundException {
        masterDataCacheService.refreshPaymentCache();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshDivisionCache() throws DataNotFoundException {
        masterDataCacheService.refreshDivisions();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshItemPerTicket() throws DataNotFoundException {
        masterDataCacheService.refreshItemPerTicket();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshBrandMetaData() {
        masterDataCacheService.refreshBrandMetaData();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshExpenseMetaData() {
        masterDataCacheService.refreshExpenseMetaDataCache();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshUnitPartnerBrandMappingData() {
        masterDataCacheService.refreshUnitPartnerBrandMapping();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshEntityAliasMappingData() {
        masterDataCacheService.refreshEntityAliasMappingData();
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshDepartmentCache() throws DataNotFoundException {
        masterDataCacheService.refreshDepartments();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshDesignationCache() throws DataNotFoundException {
        masterDataCacheService.refreshDesignations();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshTaxProfileCache() throws DataNotFoundException {
        masterDataCacheService.refreshTaxProfiles();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshDenominationCache() throws DataNotFoundException {
        masterDataCacheService.refreshDenominations();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshEmployeeCache() throws DataNotFoundException {
        masterDataCacheService.refreshEmployees();
        return true;
    }

    @Override
    public boolean refreshHodDetailCache() throws DataNotFoundException {
        masterDataCacheService.refreshHods();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshPreAuthenticatedApiCache() {
        List<String> preAuthApis = masterCacheManagementDao.findAll(PreAuthenticatedApiData.class).stream()
            .filter(preAuthenticatedApiData -> AppConstants.ACTIVE.equals(preAuthenticatedApiData.getStatus()))
            .map(PreAuthenticatedApiData::getApi).collect(Collectors.toList());
        preAuthenticatedApiCache.setPreAuthenticatedAPIs(preAuthApis);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshUnitCache(boolean flushInventory) throws DataNotFoundException {
        masterDataCacheService.refreshUnits(flushInventory);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshListDataCache() throws DataNotFoundException {
        masterDataCacheService.refreshListData();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshAddonDataCache() throws DataNotFoundException {
        masterDataCacheService.refreshAddonData();
        return true;
    }

    @Override
    public boolean refreshKioskCompanyCache() throws DataNotFoundException {
        masterDataCacheService.refreshKioskCompanies();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshProductCache() throws DataNotFoundException {
        masterDataCacheService.refreshProductCache();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshExternalAPICacheCache() throws DataNotFoundException {
        externalAPIService.refreshExternalAPICache();
        return true;
    }


    @Override
    public boolean uploadSingleLocalities(LocalityMapping locality) {
//		localityMappingDao.deleteAll();
        //it is for uploading single object
//		for (LocalityMapping locality : localities) {

//			if (locality.getObjectId() == null) {
        locality.setCreatedAt(locality.getUpdatedAt());
//			}
//		}
        localityMappingDao.save(locality);
        masterDataCacheService.refreshLocalities();
        return true;
    }

    @Override
    public boolean uploadLocalities(List<LocalityMapping> localities) {
        // this is for uploading excel file
        localityMappingDao.deleteAll();
        for (LocalityMapping locality : localities) {
            locality.setUpdatedAt(AppUtils.getCurrentTimestamp());
            if (locality.getObjectId() == null) {
                locality.setCreatedAt(locality.getUpdatedAt());
            }
        }
        localityMappingDao.saveAll(localities);
        masterDataCacheService.refreshLocalities();
        return true;
    }

    @Override
    public boolean updateLocality(LocalityMapping locality) {

        Optional<LocalityMapping> localityMappingData = localityMappingDao.findById(locality.getObjectId());
        if (localityMappingData.isPresent()) {
            locality.setUpdatedAt(AppUtils.getCurrentTimestamp());
            localityMappingDao.save(locality);
            masterDataCacheService.refreshLocalities();
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<LocalityMapping> getLocalities() {
        return new ArrayList<>(masterDataCacheService.getAllLocalities().values());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshEnvironmentPropsCache() {
        masterDataCacheService.refreshEnvironmentPropsCache();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshUnitPartnerMenuMappings() {
        masterDataCacheService.loadUnitPartnerMenuMapping();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean reloadUnitChannelPartnerMappings() {
        masterDataCacheService.loadUnitChannelPartnerMapping();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean reloadChannelPartner() throws DataNotFoundException {
        masterDataCacheService.refreshChannelPartnerCache();
        return true;
    }

    @Override
    public boolean uploadLocalities(String fileName) {
        boolean flag = false;
        Map<String, LocalityMapping> localityCache = masterDataCacheService.getAllLocalities();
        List<LocalityMapping> localities = new ArrayList<>();
        try {
            File file = new File(fileName);
            Workbook workbook = WorkbookFactory.create(file);
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet.getRow(0).getPhysicalNumberOfCells() == 9) {
                for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                    Row aRow = sheet.getRow(i);
                    String objectId = getValue(aRow, 0, null);
                    LocalityMapping mapping = (objectId == null || objectId.isEmpty()) ? new LocalityMapping()
                        : localityCache.get(objectId);
                    mapping = mapping != null ? mapping : new LocalityMapping();

                    mapping.setCountry(getValue(aRow, 1, "India"));
                    mapping.setState(getValue(aRow, 2, "Delhi"));
                    mapping.setCity(getValue(aRow, 3, "New Delhi"));
                    mapping.setLocality(getValue(aRow, 4, "NA"));
                    mapping.setPrimaryCOD(getValue(aRow, 5, "NA"));
                    mapping.setSecondaryCOD(getValue(aRow, 6, "NA"));
                    mapping.setPrimaryUnitId(getCellValue(aRow.getCell(7)));
                    mapping.setSecondaryUnitId(getCellValue(aRow.getCell(8)));

                    localities.add(mapping);
                }
                flag = uploadLocalities(localities);
            }
        } catch (Exception e) {
            LOG.error("Exception occurred during uploading mappings", e);
        }
        return flag;
    }

    private String getValue(Row row, int index, String exceptionString) {
        Cell cell = row.getCell(index, MissingCellPolicy.RETURN_NULL_AND_BLANK);
        return cell != null ? cell.getStringCellValue() : exceptionString;
    }

    private String getCellValue(Cell cell) {
        return cell != null
            ? ((cell.getCellType() == CellType.NUMERIC) ? String.valueOf((int) cell.getNumericCellValue())
            : cell.getStringCellValue())
            : "0";
    }

    @Override
    public View downloadLocalities() {
        List<LocalityMapping> localities = Lists.newArrayList(masterDataCacheService.getAllLocalities().values());
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileName = "\"Chaayos Cafe Locality Mappings.xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
                if (localities.size() > 1) {
                    String sheetName = "LocalityMappings" + AppUtils.getCurrentTimeISTStringWithNoColons();
                    int index = workbook.getSheetIndex(sheetName);
                    if (index != -1) {
                        workbook.removeSheetAt(index);
                    }
                    Sheet sheet = workbook.createSheet(sheetName);
                    sheet.setDefaultColumnWidth(20);
                    CellStyle style = generateHeaderStyle(workbook);

                    Row header = sheet.createRow(0);
                    header.createCell(0).setCellValue("objectId");
                    header.getCell(0).setCellStyle(style);
                    header.createCell(1).setCellValue("Country");
                    header.getCell(1).setCellStyle(style);
                    header.createCell(2).setCellValue("State");
                    header.getCell(2).setCellStyle(style);
                    header.createCell(3).setCellValue("City");
                    header.getCell(3).setCellStyle(style);
                    header.createCell(4).setCellValue("Locality");
                    header.getCell(4).setCellStyle(style);
                    header.createCell(5).setCellValue("Primary COD");
                    header.getCell(5).setCellStyle(style);
                    header.createCell(6).setCellValue("Secondary COD");
                    header.getCell(6).setCellStyle(style);
                    header.createCell(7).setCellValue("Primary Unit ID");
                    header.getCell(7).setCellStyle(style);
                    header.createCell(8).setCellValue("Secondary Unit ID");
                    header.getCell(8).setCellStyle(style);

                    int rowCount = 1;
                    for (LocalityMapping locality : localities) {
                        Row aRow = sheet.createRow(rowCount++);

                        setCell(aRow, 0, locality.getObjectId(), true, workbook, style);
                        setCell(aRow, 1, locality.getCountry(), true, workbook, style);
                        setCell(aRow, 2, locality.getState(), true, workbook, style);
                        setCell(aRow, 3, locality.getCity(), false, workbook, style);
                        setCell(aRow, 4, locality.getLocality(), false, workbook, style);
                        setCell(aRow, 5, locality.getPrimaryCOD(), false, workbook, style);
                        setCell(aRow, 6, locality.getSecondaryCOD(), false, workbook, style);
                        setCell(aRow, 7, locality.getPrimaryUnitId(), false, workbook, style);
                        setCell(aRow, 8, locality.getSecondaryUnitId(), false, workbook, style);

                    }
                }
            }
        };
    }

    private Cell setCell(Row aRow, int i, String value, boolean lockCell, Workbook workbook, CellStyle style) {
        Cell cell = aRow.createCell(i);
        cell.setCellValue(value);
        if (lockCell) {
//			CellStyle style = workbook.createCellStyle();
            style.setLocked(true);
        }
        return cell;
    }

    private CellStyle generateHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        style.setLocked(true);
        return style;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.service.MasterCacheManagementService#
     * refreshCancellationReason()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean refreshCancellationReason() throws DataNotFoundException {
        return masterDataCacheService.refreshCancellationReasons();
    }

    @Override
    public Set<IdCodeName> getUnitCityList() {
        return masterDataCacheService.getUnitCityList();
    }

    @Override
    public Set<IdCodeName> getUnitsOfLocation(int locationId) {
        return masterDataCacheService.getUnitsOfLocation(locationId);
    }

    @Override
    public View getMasterLocalityView() {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                response.addHeader("Content-Disposition", "attachment; filename=\"Localities.xlsx\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<LocalityWrapper> l = new ArrayList<LocalityWrapper>();
                masterDataCacheService.getAllLocalities().values().stream().forEach(u -> {
                    l.add(convertToWrapper(u));
                });
                writer.writeSheet(l, LocalityWrapper.class);
            }

        };
    }

    private LocalityWrapper convertToWrapper(LocalityMapping u) {
        LocalityWrapper w = new LocalityWrapper();
        w.setCity(u.getCity());
        w.setCountry(u.getCountry());
        w.setDefaultLocality(false);
        w.setLocality(u.getLocality());
        w.setPrimaryUnitId(Integer.parseInt(u.getPrimaryUnitId()));
        w.setPrimaryUnitName(u.getPrimaryCOD());
        if (u.getSecondaryUnitId() != null) {
            w.setSecondaryUnitId(Integer.parseInt(u.getSecondaryUnitId()));
        }
        if (u.getSecondaryCOD() != null) {
            w.setSecondaryUnitName(u.getSecondaryCOD());
        }
        w.setState(u.getState());
        return w;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void uploadLocalities(MultipartFile file) throws IOException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<LocalityWrapper> entityList = parser.createEntity(workbook.getSheetAt(0), LocalityWrapper.class,
            errors::add);
        if (errors.isEmpty()) {
            for (LocalityWrapper w : entityList) {
                LocalityMapping m = localityMappingDao.findBylocalityAndCityAndState(w.getLocality(), w.getCity(),
                    w.getState());
                if (m != null) {
                    updateLocality(m, w);
                } else {
                    addLocality(w);
                }
            }
        } else {
            LOG.info("Error Parsing Workbook for Expense, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
        }
        workbook.close();
    }

    private void addLocality(LocalityWrapper w) {
        LocalityMapping m = new LocalityMapping();
        m.setCity(w.getCity());
        m.setCountry(w.getCountry());
        m.setCreatedAt(AppUtils.getCurrentTimestamp());
        m.setLocality(w.getLocality());
        m.setPrimaryCOD(w.getPrimaryUnitName());
        m.setPrimaryUnitId(String.valueOf(w.getPrimaryUnitId()));
        m.setSecondaryCOD(w.getSecondaryUnitName());
        m.setSecondaryUnitId(String.valueOf(w.getSecondaryUnitId()));
        m.setState(w.getState());
        m.setUpdatedAt(AppUtils.getCurrentTimestamp());
        localityMappingDao.save(m);
    }

    private void updateLocality(LocalityMapping m, LocalityWrapper w) {
        m.setPrimaryCOD(w.getPrimaryUnitName());
        m.setPrimaryUnitId(String.valueOf(w.getPrimaryUnitId()));
        m.setSecondaryCOD(w.getSecondaryUnitName());
        m.setSecondaryUnitId(String.valueOf(w.getSecondaryUnitId()));
        m.setUpdatedAt(AppUtils.getCurrentTimestamp());
        localityMappingDao.save(m);
    }

    @Override
    public void refreshLocation() {
        masterDataCacheService.refreshLocations();
    }

    @Override
    public void refreshUnitPartnerBrandMetadata() {
        masterDataCacheService.refreshUnitPartnerBrandMetadata();
    }

    @Override
    public void refreshRegions() {
        masterDataCacheService.refreshRegionsMapData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshCacheReferenceValue() {
        masterDataCacheService.refreshCacheReferenceValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshUnitPartnerEdcMappings() throws  DataNotFoundException{
        masterDataCacheService.refreshUnitToPartnerEdcMapping();
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshSourceCondimentMapping() throws  DataNotFoundException{
        masterDataCacheService.refreshSourceCondimentMapping();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshUnitClosureStateMetadata() throws  DataNotFoundException{
        masterDataCacheService.refreshUnitClosureStateMetadata();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshGroupCondimentMapping() throws  DataNotFoundException{
        masterDataCacheService.refreshGroupCondimentMapping();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshUnitIpAddressCache() throws  DataNotFoundException{
        masterDataCacheService.refreshUnitTerminalDataMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void clearCustomerAppliedCouponCache(){
        masterDataCacheService.clearCustomerAppliedCouponCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshUnitMonkRecipeProfilesVersion() {
        masterDataCacheService.refreshUnitMonkRecipeProfileVersion();
    }

    @Override
    public IMap<Integer, Pair<String, String>> getUnitMonkRecipeProfilesVersion() {
        return masterDataCacheService.getUnitMonkRecipeProfileVersionImap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshWorkStationsStationCategories() {
        masterDataCacheService.refreshWorkStationsStationCategories();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshPriceCategoryWiseProductsPrice() {
        masterDataCacheService.refreshPriceCategoryWiseProductsPrice();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",propagation = Propagation.REQUIRED)
    public boolean refreshUnitDroolVersionMappingCache(){
        return masterDataCacheService.refreshUnitDroolVersionMapping();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshSpecialMilkVariantCache() {
        masterDataCacheService.refreshSpecialMilkVariantMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshDispenserItemCanisterCache() {
        masterDataCacheService.refreshDispenserCanisterItemDataMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public IMap<Integer, DispenserCanisterItemDataDto> getDispenserItemCanisterCache() {
        return masterDataCacheService.getDispenserItemCanisterCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshPriceProfileProductMapping(){
        masterDataCacheService.refreshAllPriceProfileToProductCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshCompanyBrandsMappingCache() {
        masterDataCacheService.refreshCompanyBrandsMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshUnitBrandMappingsCache() {
        masterDataCacheService.refreshUnitBrandMaps();
    }

}
