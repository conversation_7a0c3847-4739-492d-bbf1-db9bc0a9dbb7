package com.stpl.tech.master.controller;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.service.MasterCacheManagementService;
import com.stpl.tech.master.core.service.RecipeService;
import com.stpl.tech.master.core.service.TaxDataCacheService;
import com.stpl.tech.master.domain.model.DispenserCanisterItemDataDto;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.locality.model.LocalityMapping;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Set;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.MASTER_CACHE_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

/**
 * Created by Rahul Singh on 09-07-2016.
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + MASTER_CACHE_MANAGEMENT_ROOT_CONTEXT)
// 'v1/access-control-management'
public class MasterCacheManagementResources {

    Logger LOG = LoggerFactory.getLogger(MasterCacheManagementResources.class);
    public MasterCacheManagementResources() {
    	LOG.info("Inside creating MasterCacheManagementResources bean");
    }
    @Autowired
    MasterProperties props;

    @Autowired
    private MasterCacheManagementService masterCacheManagementService;

    @Autowired
    private TaxDataCacheService taxCacheService;

    @Autowired
    private RecipeService recipeService;

    @Autowired
    private TaxDataCache taxDataCache;

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    @Autowired
    private MasterDataCache masterDataCache;


    @RequestMapping(method = RequestMethod.GET, value = "refresh-master-cache")
    public boolean refreshMasterCache() throws DataNotFoundException, IOException, EmailGenerationException {
        LOG.info("Request to refresh master cache");
       return refreshCache(false);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-master-cache-with-inventory")
    public boolean refreshMasterCacheWithInventory() throws DataNotFoundException, IOException, EmailGenerationException {
        LOG.info("Request to refresh master cache");
        return refreshCache(true);
    }


    @PostConstruct
    public void refreshCacheOnReboot() throws DataNotFoundException, IOException, EmailGenerationException {
        LOG.info("Refresh master cache on reboot");
        //LOG.info("Size Of getTaxCategoryMap : {}", taxDataCache.getTaxCategoryMap().size());
        if(instance.getCluster().getMembers().size()<=1 || taxDataCache.getTaxCategoryMap().size()<=0){
           // LOG.info("Size Of getTaxCategoryMap : {}",taxDataCache.getTaxCategoryMap().size());
            refreshCache(false);
        }
    }


	public boolean refreshCache(boolean withInventory)
			throws DataNotFoundException, IOException, EmailGenerationException {
		try {
			taxCacheService.loadCache();
			int activatedRecipeIds = recipeService.refreshRecipeCache();
			masterCacheManagementService.refreshMasterCache(activatedRecipeIds >0 && withInventory);
			LOG.info("Refresh Master Cache :: SUCCESSFUL");

		} catch (Exception e) {
			LOG.error("Refresh Master Cache :: FAILED", e);
			return false;
		}
		return true;
	}

    @RequestMapping(method = RequestMethod.GET, value = "refresh-locality-cache")
    public boolean refreshLocalityCache() throws DataNotFoundException {
        LOG.info("Request to refresh localities cache");
        return masterCacheManagementService.refreshLocalityCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-tax-cache")
    public boolean refreshTaxDataCache() throws DataNotFoundException {
        LOG.info("Request to refresh master cache");
        taxCacheService.loadCache();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-recipe-cache")
    public boolean refreshRecipeCache() throws DataNotFoundException {
        LOG.info("Request to refresh recipe cache");
        return masterCacheManagementService.refreshRecipeCache();
    }

    @RequestMapping(method = RequestMethod.POST,value = "add-recipe-in-cache")
    public boolean addRecipeIntoCache(@RequestParam Integer id){
        LOG.info("Request to add recipe into cache");
        return masterCacheManagementService.addRecipeIntoCache(id);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-payment-cache")
    public boolean refreshPaymentCache() throws DataNotFoundException {
        LOG.info("Request to refresh payment cache");
        return masterCacheManagementService.refreshPaymentCache();
    }


    @RequestMapping(method = RequestMethod.GET, value = "refresh-division-cache")
    public boolean refreshDivisionCache() throws DataNotFoundException {
        LOG.info("Request to refresh division cache");
        return masterCacheManagementService.refreshDivisionCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-department-cache")
    public boolean refreshDepartmentCache() throws DataNotFoundException {
        LOG.info("Request to refresh department cache");
        return masterCacheManagementService.refreshDepartmentCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-designation-cache")
    public boolean refreshDesignationCache() throws DataNotFoundException {
        LOG.info("Request to refresh designation cache");
        return masterCacheManagementService.refreshDesignationCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-tax-profile-cache")
    public boolean refreshTaxProfileCache() throws DataNotFoundException {
        LOG.info("Request to refresh tax-profile cache");
        return masterCacheManagementService.refreshTaxProfileCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-denomination-cache")
    public boolean refreshDenominationCache() throws DataNotFoundException {
        LOG.info("Request to refresh denomination cache");
        return masterCacheManagementService.refreshDenominationCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-employee-cache")
    public boolean refreshEmployeeCache() throws DataNotFoundException {
        LOG.info("Request to refresh employee cache");
        return masterCacheManagementService.refreshEmployeeCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-hod-cache")
    public boolean refreshHodDetailCache() throws DataNotFoundException {
        LOG.info("Request to refresh HOD cache");
        return masterCacheManagementService.refreshHodDetailCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-pre-authenticated-apis-cache")
    public boolean refreshPreAuthenticatedApiCache() {
        LOG.info("Request to refresh-pre-authenticated-apis-cache");
        return masterCacheManagementService.refreshPreAuthenticatedApiCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-unit-cache")
    public boolean refreshUnitCache() throws DataNotFoundException {
        LOG.info("Request to refresh unit cache");
        return masterCacheManagementService.refreshUnitCache(false);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-unit-cache-with-inventory")
    public boolean refreshUnitCacheWithInventory() throws DataNotFoundException {
        LOG.info("Request to refresh unit cache");
        return masterCacheManagementService.refreshUnitCache(true);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-list-data-cache")
    public boolean refreshListDataCache() throws DataNotFoundException {
        LOG.info("Request to refresh list data cache");
        return masterCacheManagementService.refreshListDataCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-addon-data-cache")
    public boolean refreshAddonDataCache() throws DataNotFoundException {
        LOG.info("Request to refresh addon data cache");
        return masterCacheManagementService.refreshAddonDataCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-product-cache")
    public boolean refreshProductCache() throws DataNotFoundException {
        LOG.info("Request to refresh product cache");
        return masterCacheManagementService.refreshProductCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-cancellation-reason")
    public boolean refreshCancellationReason() throws DataNotFoundException {
        LOG.info("Request to refresh cancellation reason cache");
        return masterCacheManagementService.refreshCancellationReason();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-kiosk-companies")
    public boolean refreshKioskCompanies() throws DataNotFoundException {
        LOG.info("Request to refresh kiosk cache");
        return masterCacheManagementService.refreshKioskCompanyCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-external-api")
    public boolean refreshExternalAPI() throws DataNotFoundException {
        LOG.info("Request to refresh external api cache");
        return masterCacheManagementService.refreshExternalAPICacheCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-environment-props")
    public boolean refreshEnvironmentPropsCache() throws DataNotFoundException {
        LOG.info("Request to refresh environment props cache");
        return masterCacheManagementService.refreshEnvironmentPropsCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-locations")
    public boolean refreshLocation() throws DataNotFoundException {
        LOG.info("Request to refresh Location");
        masterCacheManagementService.refreshLocation();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-item-per-ticket")
    public boolean refreshItemPerTicket() throws DataNotFoundException {
        LOG.info("Request to refresh Item Per Ticket");
        masterCacheManagementService.refreshItemPerTicket();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-brand-meta-data")
    public boolean refreshBrandMetaData() throws DataNotFoundException {
        LOG.info("Request to refresh Brand Meta Data");
        masterCacheManagementService.refreshBrandMetaData();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-expense-meta-data")
    public boolean refreshExpenseMetaData() throws DataNotFoundException {
        LOG.info("Request to refresh Expense Meta Data");
        masterCacheManagementService.refreshExpenseMetaData();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-entity-alias-data")
    public boolean refreshEntityAliasMapping() throws DataNotFoundException {
        LOG.info("Request to refresh entity alias mapping data");
        masterCacheManagementService.refreshEntityAliasMappingData();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-partner-brand-mapping-data")
    public boolean refreshUnitPartnerBrandMappingData() throws DataNotFoundException {
        LOG.info("Request to refresh unit partner brand mapping Data");
        masterCacheManagementService.refreshUnitPartnerBrandMappingData();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-partner-menu-mapping-data")
    public boolean getUnitPartnerMenuMappings() throws DataNotFoundException {
        LOG.info("Request to refresh unit partner menu mapping Data");
        masterCacheManagementService.refreshUnitPartnerMenuMappings();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-unit-channel-partner")
    public boolean reloadUnitChannelPartnerMappings() {
        masterCacheManagementService.reloadUnitChannelPartnerMappings();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-channel-partner")
    public boolean reloadChannelPartner() throws DataNotFoundException {
        masterCacheManagementService.reloadChannelPartner();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-unit-partner-brand-mapping-metadata")
    public boolean reloadUnitPartnerBrandMetadata() throws DataNotFoundException {
        masterCacheManagementService.refreshUnitPartnerBrandMetadata();
        return true;
    }

    ///////////////////////////////////////////////////////////////////////////////////////

    /////////////// TODO move these APIs as they don't belong here   //////////////////////

    ///////////////////////////////////////////////////////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "upload/localities-obj", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public boolean uploadLocalities(@RequestBody LocalityMapping localities) throws DataNotFoundException {
        LOG.info("Request to Upload single  Localities");
        return masterCacheManagementService.uploadSingleLocalities(localities);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload/localities", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public boolean uploadLocalities(@RequestParam(value = "file") final MultipartFile file) throws DataNotFoundException {
        LOG.info("Request to Upload bulk Localities");
        return masterCacheManagementService.uploadLocalities(writeLocalityFile(props.getBasePath(), file, LOG, "Chaayos_Locality_Mappings.csv"));
    }

    @RequestMapping(method = RequestMethod.GET, value = "download/localities")
    public View downloadLocalities() throws DataNotFoundException {
        LOG.info("Request to Download Localities");
        return masterCacheManagementService.downloadLocalities();
    }

    @RequestMapping(method = RequestMethod.GET, value = "localities")
    public List<LocalityMapping> getLocalities() throws DataNotFoundException {
        LOG.info("Request to Download Localities");
        return masterCacheManagementService.getLocalities();
    }
    @RequestMapping(method = RequestMethod.POST, value = "update/locality", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public boolean updateLocality(@RequestBody LocalityMapping locality) throws DataNotFoundException {
        LOG.info("Request to update locality");
        return masterCacheManagementService.updateLocality(locality);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-cities")
    public Set<IdCodeName> getAllCities() throws DataNotFoundException {
        LOG.info("Request to get city List");
        return masterCacheManagementService.getUnitCityList();
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-city-mapping")
    public Set<IdCodeName> getAllCafesOfCity(@RequestBody final int locationId) throws DataNotFoundException {
        LOG.info("Request to get cafes of location id " + locationId);
        return masterCacheManagementService.getUnitsOfLocation(locationId);
    }

    public static String writeLocalityFile(String rootPath, MultipartFile file, Logger log, String name) {
        if (!file.isEmpty()) {
            log.info("File Size is ::: ", file.getSize());
            try {
                return AppUtils.write(file.getBytes(), rootPath, "localityMappings", name, log);
            } catch (IOException e) {
                log.error("Could not read file ::::", e);
                return null;
            }
        } else {
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-region-cache")
    public boolean refreshRegionCache() throws DataNotFoundException {
        LOG.info("Request to refresh Region Cache");
        masterCacheManagementService.refreshRegions();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-cache-reference")
    public boolean refreshCacheReferenceValue(){
        LOG.info("Request to refresh cache reference");
        masterCacheManagementService.refreshCacheReferenceValue();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-unit-partner-edc-mapping")
    public boolean refreshUnitPartnerEdcMappings() throws DataNotFoundException {
        LOG.info("Request to refresh unit to partner edc mapping Cache");
        masterCacheManagementService.refreshUnitPartnerEdcMappings();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-src-condiment-mapping")
    public boolean refreshSourceCondimentMapping() throws DataNotFoundException {
        LOG.info("Request to source to condiment mapping Cache");
        masterCacheManagementService.refreshSourceCondimentMapping();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-unit-closure-metadata")
    public boolean refreshUnitClosureStateMetadata() throws DataNotFoundException {
        LOG.info("Request to refresh unit closure metadata Cache");
        masterCacheManagementService.refreshUnitClosureStateMetadata();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-group-condiment-mapping")
    public boolean refreshGroupCondimentMapping() throws DataNotFoundException {
        LOG.info("Request to refresh product to condiment mapping Cache");
        masterCacheManagementService.refreshGroupCondimentMapping();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-unit-ip-address-cache")
    public boolean refreshUnitIpAddressCache() throws DataNotFoundException {
        LOG.info("Request to refresh unit ip address Cache");
        masterCacheManagementService.refreshUnitIpAddressCache();
        return true;
    }

    @GetMapping("/refresh-unit-closure-state-query")
    public boolean refreshUnitClosureStateQuery() throws DataNotFoundException{
        LOG.info("Request to refresh unit closure state query");
        masterCacheManagementService.refreshUnitClosureStateMetadata();
        return true;
    }

    @GetMapping("clear-customer-applied-coupon-cache")
    public boolean clearCustomerAppliedCouponCache(){
        LOG.info("Request to clear Customer Applied Coupon Cache");
        masterCacheManagementService.clearCustomerAppliedCouponCache();
        return true;
    }

    @GetMapping("refresh-unit-monk-recipe-profile-version")
    public boolean refreshUnitMonkRecipeProfilesVersion() {
        LOG.info("Request to refreshUnitMonkRecipeProfilesVersion");
        masterCacheManagementService.refreshUnitMonkRecipeProfilesVersion();
        return true;
    }

    @GetMapping("refresh-work-stations-station-categories")
    public boolean refreshWorkStationsStationCategories() {
        LOG.info("Request to refreshWorkStationsStationCategories");
        masterCacheManagementService.refreshWorkStationsStationCategories();
        return true;
    }

    @GetMapping("refresh-price-category-wise-product-price")
    public boolean refreshPriceCategoryWiseProductsPrice() {
        LOG.info("Request to refreshPriceCategoryWiseProductsPrice");
        masterCacheManagementService.refreshPriceCategoryWiseProductsPrice();
        return true;
    }

    @GetMapping("get-unit-monk-recipe-profile-version")
    public IMap<Integer, Pair<String, String>> getUnitMonkRecipeProfilesVersion() {
        LOG.info("Request to getUnitMonkRecipeProfilesVersion");
        return masterCacheManagementService.getUnitMonkRecipeProfilesVersion();
    }

    @GetMapping("refresh-unit-drool-version-mapping")
    public boolean refreshUnitDroolVersionMappingCache() {
        LOG.info("Request to refresh unit drool version mapping cache");
        return masterCacheManagementService.refreshUnitDroolVersionMappingCache();
    }

    @GetMapping("refresh-special-milk-variant-cache")
    public void refreshSpecialMilkVariantCache() {
        LOG.info("Request to refresh special Milk Variant Cache");
        masterCacheManagementService.refreshSpecialMilkVariantCache();
    }

    @GetMapping("refresh-dispenser-canister-item-cache")
    public void refreshDispenserItemCanisterCache() {
        LOG.info("Request to refresh Dispenser Item Canister Cache");
        masterCacheManagementService.refreshDispenserItemCanisterCache();
    }

    @GetMapping("get-dispenser-canister-item-cache")
    public IMap<Integer, DispenserCanisterItemDataDto> getDispenserItemCanisterCache() {
        LOG.info("Request to get Dispenser Item Canister Cache");
        return masterCacheManagementService.getDispenserItemCanisterCache();
    }

    @GetMapping("refresh-price-profile-products-cache")
    public void refreshPriceProfileProductsCache() {
        LOG.info("Request to refresh Price Profile Products Cache");
        masterCacheManagementService.refreshPriceProfileProductMapping();
    }

    @GetMapping(value = "refresh-company-brands-mapping-cache")
    public void refreshCompanyBrandsMappingCache() {
        LOG.info("Request to refresh Company Brands Mapping Cache");
        masterCacheManagementService.refreshCompanyBrandsMappingCache();
    }

    @GetMapping(value = "refresh-unit-brand-mappings-cache")
    public void refreshUnitBrandsMappingCache() {
        LOG.info("Request to refresh Unit Brands Mapping Cache");
        masterCacheManagementService.refreshUnitBrandMappingsCache();
    }

}
