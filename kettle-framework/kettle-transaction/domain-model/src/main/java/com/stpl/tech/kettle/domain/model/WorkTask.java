package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * orderId
 * ItemId
 * billingServerTime,
 * unitId,
 * type
 * employeeId
 * productId,
 * itemQuantity,
 * dimension,
 * source,
 * timeToAcknowledge,
 * timeToStart
 * timeToProcess,
 * timeToRemove,
 * timeToDispatch
 * timeToProcessByMachine = timeToProcessByMachine + timeToDispacth,
 * cooktop station
 * stationEventsForOrder
 */
@JsonIgnoreProperties(ignoreUnknown=true)
public class WorkTask implements Cloneable, Comparable<WorkTask> {

    private int taskId;//orderItemId
    private int orderId; //orderIdAtserver
    private String generateOrderId;//generatedOrderId
    private OrderItem orderItem;
    private boolean combo;
    private Integer comboOrderItemId;
    private long orderCreationTime;


    private Date orderCreationDateTime;
    private long orderAcknowledgeTime;
    private long taskCreationTime; //its current time not delta
    private String assignedMonk;
    private String assignedMonkForEvent;
    private boolean isSplittedOrder = false;
    private int linkedTaskId;
    private boolean isManualCreated = false;
    private boolean isHighPriorityTask = false;
    private boolean isRemakeChai = false;
    private int remakeCount = 0;
    private long taskCompletionTime;
    private String customerName;
    private String state;
    private String type;
    private int productId;
    private boolean isProcessing = false;
    private long firstAssignAttemptTime;

    private String distributedState;
    private int WsEmployeeId = 0;
    private int noOfManualRefreshAttempts = 0;
    private Integer tokenNumber;
    private Boolean tableOrder;
    private long taskStartingTime;
    private Boolean isSteepedChai;
    private Boolean isReceipePanChange;

    private boolean isMonkProcessingDone = false;
    private long timeOnmonkProcessingDone = 0;
    private boolean bulkOrder = false;
    private long steepTimeTat;
    private String orderRemark;
    private String attribute; // display a banner if this field is FAST_15
    private int milkQuantity;
    private int waterQuantity;
    private Integer brandId;
    private String brandName;
    // private Integer serialNo;
    private String manualTaskOrderId;

    private boolean isClubbed; //task containing clubbed tasks will be marked true
    public List<WorkTask> clubbedTaskList = new ArrayList<>();
    private List<String> subWorkTasks;
    private String thaliComboTaskName;
    private String sourceVersion;
    private Boolean orderInstructionSeen;
    private String beforeCancelRequestedState;

    private String chaiCompletionStats;
    private boolean notReceived19FromMonk;

    private String stationCategory;

    private boolean isLastTrySuccessfulForReDistribution;


    /**
     * Eg : SWIGGY Bolt 10 minutes Delivery
     */
    private boolean prioritizedOrder;

    private String monkVersion;

    private String recipeString;
    private Integer requiredNumberOfBoils;
    private Integer originalMilkQuantity;

    public Date getOrderCreationDateTime() {
        return orderCreationDateTime;
    }

    public void setOrderCreationDateTime(Date orderCreationDateTime) {
        this.orderCreationDateTime = orderCreationDateTime;
    }


    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public int getMilkQuantity() {
        return milkQuantity;
    }

    public void setMilkQuantity(int milkQuantity) {
        this.milkQuantity = milkQuantity;
    }

    public int getWaterQuantity() {
        return waterQuantity;
    }

    public void setWaterQuantity(int waterQuantity) {
        this.waterQuantity = waterQuantity;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }

    public String getOrderRemark() {
        return orderRemark;
    }

    public void setOrderRemark(String orderRemark) {
        this.orderRemark = orderRemark;
    }

    private WorkTask(int taskId) {
        this.taskId = taskId;
    }

    public String getGenerateOrderId() {
        return generateOrderId;
    }

    private ArrayList<Integer> progressStatusCodes = new ArrayList<>();

    private double milkQuantityDispensed = 0;

    public void setGenerateOrderId(String generateOrderId) {
        this.generateOrderId = generateOrderId;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public String source;

    public long getTaskCompletionTime() {
        return taskCompletionTime;
    }

    public void setTaskCompletionTime(long taskCompletionTime) {
        this.taskCompletionTime = taskCompletionTime;
    }

    public boolean isSplittedOrder() {
        return isSplittedOrder;
    }

    public void setSplittedOrder(boolean splittedOrder) {
        isSplittedOrder = splittedOrder;
    }

    public int getLinkedTaskId() {
        return linkedTaskId;
    }

    public void setLinkedTaskId(int linkedTaskId) {
        this.linkedTaskId = linkedTaskId;
    }

    public int getWsEmployeeId() {
        return WsEmployeeId;
    }

    public void setWsEmployeeId(int wsEmployeeId) {
        WsEmployeeId = wsEmployeeId;
    }

    public boolean isManualCreated() {
        return isManualCreated;
    }

    public void setManualCreated(boolean manualCreated) {
        isManualCreated = manualCreated;
    }

    public boolean isHighPriorityTask() {
        return isHighPriorityTask;
    }

    public void setHighPriorityTask(boolean highPriorityTask) {
        isHighPriorityTask = highPriorityTask;
    }

    public boolean isRemakeChai() {
        return isRemakeChai;
    }

    public void setRemakeChai(boolean remakeChai) {
        isRemakeChai = remakeChai;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public WorkTask() {
    }

    public OrderItem getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(OrderItem orderItem) {
        this.orderItem = orderItem;
    }


    public int getTaskId() {
        return taskId;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean mCombo) {
        this.combo = mCombo;
    }

    public Integer getComboOrderItemId() {
        return comboOrderItemId;
    }

    public void setComboOrderItemId(Integer comboOrderItemId) {
        this.comboOrderItemId = comboOrderItemId;
    }

    public long getOrderCreationTime() {
        return orderCreationTime;
    }

    public void setOrderCreationTime(long orderCreationTime) {
        this.orderCreationTime = orderCreationTime;
    }

    public long getTaskCreationTime() {
        return taskCreationTime;
    }

    public void setTaskCreationTime(long taskCreationTime) {
        this.taskCreationTime = taskCreationTime;
    }

    public String getCustomerName() {
        return customerName;
    }

//    public Integer getSerialNo() {
//        return serialNo;
//    }
//
//    public void setSerialNo(Integer serialNo) {
//        this.serialNo = serialNo;
//    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public boolean isProcessing() {
        return isProcessing;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setProcessing(boolean processing) {
        isProcessing = processing;
    }


/*   @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        } else if (obj instanceof WorkTask) {
            return (((WorkTask) obj).getTaskId() == this.getTaskId());
        }
        return false;
    }*/


    @Override
    public String toString() {
        return "Work task: " + taskId + "#" + orderItem.getProductName() + "#" + orderItem.getProductId() + "#" + orderItem.getQuantity();
    }


    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public int getNoOfManualRefreshAttempts() {
        return noOfManualRefreshAttempts;
    }

    public void setNoOfManualRefreshAttempts(int noOfManualRefreshAttempts) {
        this.noOfManualRefreshAttempts = noOfManualRefreshAttempts;
    }

    public Integer getTokenNumber() {
        return tokenNumber;
    }

    public void setTokenNumber(Integer tokenNumber) {
        this.tokenNumber = tokenNumber;
    }

    public Boolean getTableOrder() {
        return tableOrder;
    }

    public void setTableOrder(Boolean tableOrder) {
        this.tableOrder = tableOrder;
    }

    public long getTaskStartingTime() {
        return taskStartingTime;
    }

    public void setTaskStartingTime(long taskStartingTime) {
        this.taskStartingTime = taskStartingTime;
    }

    public void setOrderAcknowledgeTime(long orderAcknowledgeTime) {
        this.orderAcknowledgeTime = orderAcknowledgeTime;
    }

    public Boolean isSteepedChai() {
        return isSteepedChai;
    }

    public void setSteepedChai(boolean steepedChai) {
        isSteepedChai = steepedChai;
    }

    public Boolean isReceipePanChange() {
        return isReceipePanChange;
    }

    public void setReceipePanChange(boolean receipePanChange) {
        isReceipePanChange = receipePanChange;
    }

    public long getFirstAssignAttemptTime() {
        return firstAssignAttemptTime;
    }

    public void setFirstAssignAttemptTime(long firstAssignAttemptTime) {
        this.firstAssignAttemptTime = firstAssignAttemptTime;
    }

    public Boolean getSteepedChai() {
        return isSteepedChai;
    }

    public void setSteepedChai(Boolean steepedChai) {
        isSteepedChai = steepedChai;
    }

    public ArrayList<Integer> getProgressStatusCodes() {
        return progressStatusCodes;
    }

    public void setProgressStatusCodes(ArrayList<Integer> progressStatusCodes) {
        this.progressStatusCodes = progressStatusCodes;
    }

    public double getMilkQuantityDispensed() {
        return milkQuantityDispensed;
    }

    public void setMilkQuantityDispensed(double milkQuantityDispensed) {
        this.milkQuantityDispensed = milkQuantityDispensed;
    }

    //For steep chai timer start point
    public boolean isMonkProcessingDone() {
        return isMonkProcessingDone;
    }

    public void setMonkProcessingDone(boolean monkProcessingDone) {
        isMonkProcessingDone = monkProcessingDone;
        if (isMonkProcessingDone) {
            timeOnmonkProcessingDone = System.currentTimeMillis();
        }
    }

    public long getTimeOnmonkProcessingDone() {
        return timeOnmonkProcessingDone;
    }

    public void setTimeOnmonkProcessingDone(long timeOnmonkProcessingDone) {
        this.timeOnmonkProcessingDone = timeOnmonkProcessingDone;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof WorkTask)) return false;

        WorkTask workTask = (WorkTask) o;

        return this.taskId == workTask.taskId;

    }

    @Override
    public int hashCode() {
        return taskId;
    }

    public void setBulkOrder(boolean bulkOrder) {
        this.bulkOrder = bulkOrder;
    }

    public boolean isBulkOrder() {
        return bulkOrder;
    }

    public long getSteepTaT() {
        return steepTimeTat;
    }

    public void setSteepTaT(long timeInMilliseconds) {
        this.steepTimeTat = timeInMilliseconds;
    }

    public void setRemakeCount(int remakeCount) {
        this.remakeCount = remakeCount;

    }

    public int getRemakeCount() {
        return remakeCount;
    }

    public boolean isClubbed() {
        return isClubbed;
    }

    public void setClubbed(boolean clubbed) {
        isClubbed = clubbed;
    }

    public List<WorkTask> getClubbedTaskList() {
        return clubbedTaskList;
    }

    public void addToClubbedTaskList(WorkTask clubbedTask) {
        this.clubbedTaskList.add(clubbedTask);
    }

    public String getManualTaskOrderId() {
        return manualTaskOrderId;
    }

    public void setManualTaskOrderId(String manualTaskOrderId) {
        this.manualTaskOrderId = manualTaskOrderId;
    }

    public List<String> getSubWorkTasks() {
        return subWorkTasks;
    }

    public void setSubWorkTasks(List<String> subWorkTasks) {
        this.subWorkTasks = subWorkTasks;
    }

    public String getThaliComboTaskName() {
        return thaliComboTaskName;
    }

    public void setThaliComboTaskName(String thaliComboTaskName) {
        this.thaliComboTaskName = thaliComboTaskName;
    }

    public String getSourceVersion() {
        return sourceVersion;
    }

    public void setSourceVersion(String sourceVersion) {
        this.sourceVersion = sourceVersion;
    }

    public Boolean getOrderInstructionSeen() {
        return orderInstructionSeen;
    }

    public void setOrderInstructionSeen(Boolean orderInstructionSeen) {
        this.orderInstructionSeen = orderInstructionSeen;
    }

    public String getChaiCompletionStats() {
        return chaiCompletionStats;
    }

    public void setChaiCompletionStats(String chaiCompletionStats) {
        this.chaiCompletionStats = chaiCompletionStats;
    }

    public boolean getNotReceived19FromMonk() {
        return notReceived19FromMonk;
    }

    public void setNotReceived19FromMonk(boolean notReceived19FromMonk) {
        this.notReceived19FromMonk = notReceived19FromMonk;
    }

    public String getStationCategory() {
        return stationCategory;
    }

    public void setStationCategory(String stationCategory) {
        this.stationCategory = stationCategory;
    }

    public boolean isLastTrySuccessfulForReDistribution() {
        return isLastTrySuccessfulForReDistribution;
    }

    public void setLastTrySuccessfulForReDistribution(boolean lastTrySuccessfulForReDistribution) {
        isLastTrySuccessfulForReDistribution = lastTrySuccessfulForReDistribution;
    }

    public boolean isPrioritizedOrder() {
        return prioritizedOrder;
    }

    public void setPrioritizedOrder(boolean prioritizedOrder) {
        this.prioritizedOrder = prioritizedOrder;
    }

    public String getMonkVersion() {
        return monkVersion;
    }

    public void setMonkVersion(String monkVersion) {
        this.monkVersion = monkVersion;
    }

    public String getRecipeString() {
        return recipeString;
    }

    public void setRecipeString(String recipeString) {
        this.recipeString = recipeString;
    }

    public Integer getRequiredNumberOfBoils() {
        return requiredNumberOfBoils;
    }

    public void setRequiredNumberOfBoils(Integer requiredNumberOfBoils) {
        this.requiredNumberOfBoils = requiredNumberOfBoils;
    }

    public Integer getOriginalMilkQuantity() {
        return originalMilkQuantity;
    }

    public void setOriginalMilkQuantity(Integer originalMilkQuantity) {
        this.originalMilkQuantity = originalMilkQuantity;
    }

    @Override
    public int compareTo(WorkTask otherWorkTask) {
        // checking if special Order then keeping at the start
        if (this.isPrioritizedOrder() && !otherWorkTask.isPrioritizedOrder()) {
            return -1;
        } else if (!this.isPrioritizedOrder() && otherWorkTask.isPrioritizedOrder()) {
            return 1;
        } else {
            return Long.compare(this.getTaskCreationTime(), otherWorkTask.getTaskCreationTime());
        }
    }
}
